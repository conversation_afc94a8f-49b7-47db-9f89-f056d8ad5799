"""
PDF智能解析器 - 结合PyMuPDF和RapidLayout的PDF解析
"""
import os
import fitz  # PyMuPDF
from PIL import Image
import numpy as np
from typing import List, Dict, Tuple, Any, Optional
import logging
from pathlib import Path

try:
    from ..config.settings import settings
    from ..utils.layout_analyzer import LayoutAnalyzer
except ImportError:
    from config.settings import settings
    from utils.layout_analyzer import LayoutAnalyzer

logger = logging.getLogger(__name__)

class PDFParser:
    """PDF智能解析器"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化PDF解析器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir) if output_dir else settings.TEMP_DIR
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化布局分析器
        self.layout_analyzer = LayoutAnalyzer()
        
        # PDF转图像参数
        self.dpi = settings.PDF_DPI
        self.format = settings.PDF_FORMAT.lower()
        
        logger.info(f"PDF parser initialized with output dir: {self.output_dir}")
    
    def parse_pdf(self, pdf_path: str, save_images: bool = True) -> List[Dict[str, Any]]:
        """
        解析PDF文件
        
        Args:
            pdf_path: PDF文件路径
            save_images: 是否保存图像文件
            
        Returns:
            页面解析结果列表
        """
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        logger.info(f"Starting PDF parsing: {pdf_path}")
        
        try:
            # 打开PDF文件
            pdf_document = fitz.open(str(pdf_path))
            page_results = []
            
            for page_index in range(len(pdf_document)):
                logger.info(f"Processing page {page_index + 1}/{len(pdf_document)}")
                
                # 解析单页
                page_result = self._parse_page(
                    pdf_document[page_index], 
                    page_index, 
                    pdf_path.stem,
                    save_images
                )
                page_results.append(page_result)
            
            pdf_document.close()
            
            logger.info(f"PDF parsing completed: {len(page_results)} pages processed")
            return page_results
            
        except Exception as e:
            logger.error(f"PDF parsing failed: {e}")
            raise
    
    def _parse_page(self, page: fitz.Page, page_index: int, 
                   pdf_name: str, save_images: bool) -> Dict[str, Any]:
        """
        解析单个页面
        
        Args:
            page: PyMuPDF页面对象
            page_index: 页面索引
            pdf_name: PDF文件名
            save_images: 是否保存图像
            
        Returns:
            页面解析结果
        """
        try:
            # 将页面转换为高分辨率图像
            matrix = fitz.Matrix(self.dpi / 72, self.dpi / 72)  # 缩放矩阵
            pix = page.get_pixmap(matrix=matrix)
            
            # 转换为PIL图像
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))
            
            # 保存页面图像
            page_image_path = None
            if save_images:
                page_image_path = self.output_dir / f"{pdf_name}_page_{page_index:03d}.{self.format}"
                image.save(page_image_path)
            
            # 布局分析
            layout_result = self.layout_analyzer.analyze_layout(image)
            
            # 提取各类型区域
            regions = self.layout_analyzer.extract_regions(image, layout_result['detections'])
            
            # 保存区域图像
            region_paths = {}
            if save_images:
                region_paths = self._save_regions(regions, pdf_name, page_index)
            
            # 按位置排序检测结果
            sorted_detections = self.layout_analyzer.sort_detections_by_position(
                layout_result['detections']
            )
            
            # 构建页面结果
            page_result = {
                'page_index': page_index,
                'page_image_path': str(page_image_path) if page_image_path else None,
                'image_size': image.size,
                'layout_analysis': layout_result,
                'regions': regions,
                'region_paths': region_paths,
                'sorted_detections': sorted_detections,
                'statistics': self.layout_analyzer.get_layout_statistics(layout_result['detections'])
            }
            
            # 保存可视化结果
            if save_images:
                vis_path = self.output_dir / f"{pdf_name}_page_{page_index:03d}_layout.{self.format}"
                self.layout_analyzer.visualize_layout(
                    image, 
                    layout_result['detections'], 
                    str(vis_path)
                )
                page_result['visualization_path'] = str(vis_path)
            
            return page_result
            
        except Exception as e:
            logger.error(f"Page parsing failed for page {page_index}: {e}")
            raise
    
    def _save_regions(self, regions: Dict[str, List[Dict]], 
                     pdf_name: str, page_index: int) -> Dict[str, List[str]]:
        """
        保存区域图像
        
        Args:
            regions: 区域字典
            pdf_name: PDF文件名
            page_index: 页面索引
            
        Returns:
            区域图像路径字典
        """
        region_paths = {}
        
        for class_name, region_list in regions.items():
            region_paths[class_name] = []
            
            for i, region_info in enumerate(region_list):
                # 生成文件名
                filename = f"{pdf_name}_page_{page_index:03d}_{class_name}_{i:02d}.{self.format}"
                file_path = self.output_dir / filename
                
                # 保存图像
                region_info['image'].save(file_path)
                region_paths[class_name].append(str(file_path))
        
        return region_paths
    
    def extract_text_content(self, pdf_path: str) -> List[Dict[str, Any]]:
        """
        提取PDF文本内容（作为备用方法）
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            文本内容列表
        """
        try:
            pdf_document = fitz.open(pdf_path)
            text_content = []
            
            for page_index in range(len(pdf_document)):
                page = pdf_document[page_index]
                
                # 提取文本
                text = page.get_text()
                
                # 提取文本块
                text_blocks = page.get_text("dict")
                
                page_content = {
                    'page_index': page_index,
                    'text': text,
                    'text_blocks': text_blocks,
                    'char_count': len(text),
                    'word_count': len(text.split()) if text else 0
                }
                
                text_content.append(page_content)
            
            pdf_document.close()
            return text_content
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            raise
    
    def get_pdf_metadata(self, pdf_path: str) -> Dict[str, Any]:
        """
        获取PDF元数据
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            PDF元数据
        """
        try:
            pdf_document = fitz.open(pdf_path)
            metadata = pdf_document.metadata
            
            pdf_info = {
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'subject': metadata.get('subject', ''),
                'creator': metadata.get('creator', ''),
                'producer': metadata.get('producer', ''),
                'creation_date': metadata.get('creationDate', ''),
                'modification_date': metadata.get('modDate', ''),
                'page_count': len(pdf_document),
                'file_size': os.path.getsize(pdf_path)
            }
            
            pdf_document.close()
            return pdf_info
            
        except Exception as e:
            logger.error(f"Metadata extraction failed: {e}")
            return {}
    
    def cleanup_temp_files(self, keep_final_results: bool = True):
        """
        清理临时文件
        
        Args:
            keep_final_results: 是否保留最终结果文件
        """
        try:
            if not keep_final_results:
                # 删除所有临时文件
                for file_path in self.output_dir.glob("*"):
                    if file_path.is_file():
                        file_path.unlink()
                logger.info("All temporary files cleaned up")
            else:
                # 只删除中间处理文件，保留最终结果
                patterns = ["*_layout.*", "*_temp_*"]
                for pattern in patterns:
                    for file_path in self.output_dir.glob(pattern):
                        if file_path.is_file():
                            file_path.unlink()
                logger.info("Intermediate files cleaned up")
                
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

# 添加缺失的导入
import io
