"""
布局分析器 - 基于RapidLayout的页面布局分析
"""
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from PIL import Image
import logging

try:
    from rapid_layout import RapidLayout, VisLayout
except ImportError:
    logging.warning("rapid_layout not installed. Please install it for layout analysis.")
    RapidLayout = None
    VisLayout = None

try:
    from ..config.settings import settings
except ImportError:
    from config.settings import settings

logger = logging.getLogger(__name__)

class LayoutAnalyzer:
    """页面布局分析器"""
    
    def __init__(self, conf_threshold: float = None, model_type: str = None):
        """
        初始化布局分析器
        
        Args:
            conf_threshold: 置信度阈值
            model_type: 模型类型
        """
        self.conf_threshold = conf_threshold or settings.LAYOUT_CONF_THRESHOLD
        self.model_type = model_type or settings.LAYOUT_MODEL_TYPE
        
        if RapidLayout is None:
            raise ImportError("rapid_layout is required for layout analysis")
        
        try:
            self.layout_engine = RapidLayout(
                conf_thres=self.conf_threshold,
                model_type=self.model_type
            )
            logger.info(f"Layout analyzer initialized with model: {self.model_type}")
        except Exception as e:
            logger.error(f"Failed to initialize layout analyzer: {e}")
            raise
    
    def analyze_layout(self, image: Image.Image) -> Dict[str, Any]:
        """
        分析页面布局
        
        Args:
            image: PIL图像对象
            
        Returns:
            包含检测结果的字典
        """
        try:
            # 执行布局检测
            boxes, scores, class_names, elapse = self.layout_engine(image)
            
            # 组织检测结果
            detections = []
            for i, (box, score, class_name) in enumerate(zip(boxes, scores, class_names)):
                detection = {
                    'id': i,
                    'bbox': box,  # [x1, y1, x2, y2]
                    'score': float(score),
                    'class': class_name,
                    'area': (box[2] - box[0]) * (box[3] - box[1])
                }
                detections.append(detection)
            
            # 按类型分组
            grouped_detections = self._group_detections_by_type(detections)
            
            result = {
                'detections': detections,
                'grouped': grouped_detections,
                'processing_time': elapse,
                'total_objects': len(detections)
            }
            
            logger.info(f"Layout analysis completed: {len(detections)} objects detected in {elapse:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"Layout analysis failed: {e}")
            raise
    
    def _group_detections_by_type(self, detections: List[Dict]) -> Dict[str, List[Dict]]:
        """
        按类型分组检测结果
        
        Args:
            detections: 检测结果列表
            
        Returns:
            按类型分组的检测结果
        """
        grouped = {}
        for detection in detections:
            class_name = detection['class']
            if class_name not in grouped:
                grouped[class_name] = []
            grouped[class_name].append(detection)
        
        # 按面积排序（大到小）
        for class_name in grouped:
            grouped[class_name].sort(key=lambda x: x['area'], reverse=True)
        
        return grouped
    
    def extract_regions(self, image: Image.Image, detections: List[Dict]) -> Dict[str, List[Image.Image]]:
        """
        根据检测结果提取图像区域
        
        Args:
            image: 原始图像
            detections: 检测结果列表
            
        Returns:
            按类型分组的图像区域
        """
        regions = {}
        
        for detection in detections:
            class_name = detection['class']
            bbox = detection['bbox']
            
            # 裁剪图像区域
            region = image.crop(bbox)
            
            if class_name not in regions:
                regions[class_name] = []
            regions[class_name].append({
                'image': region,
                'bbox': bbox,
                'id': detection['id'],
                'score': detection['score']
            })
        
        return regions
    
    def visualize_layout(self, image: Image.Image, detections: List[Dict], 
                        save_path: str = None) -> np.ndarray:
        """
        可视化布局检测结果
        
        Args:
            image: 原始图像
            detections: 检测结果列表
            save_path: 保存路径（可选）
            
        Returns:
            可视化结果图像
        """
        if VisLayout is None:
            logger.warning("VisLayout not available for visualization")
            return np.array(image)
        
        try:
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 准备检测数据
            boxes = [det['bbox'] for det in detections]
            scores = [det['score'] for det in detections]
            class_names = [det['class'] for det in detections]
            
            # 绘制检测框
            vis_image = VisLayout.draw_detections(cv_image, boxes, scores, class_names)
            
            if vis_image is not None and save_path:
                cv2.imwrite(save_path, vis_image)
                logger.info(f"Visualization saved to: {save_path}")
            
            return vis_image if vis_image is not None else cv_image
            
        except Exception as e:
            logger.error(f"Visualization failed: {e}")
            return np.array(image)
    
    def filter_detections(self, detections: List[Dict], 
                         min_score: float = 0.5,
                         target_classes: List[str] = None) -> List[Dict]:
        """
        过滤检测结果
        
        Args:
            detections: 检测结果列表
            min_score: 最小置信度
            target_classes: 目标类别列表
            
        Returns:
            过滤后的检测结果
        """
        filtered = []
        
        for detection in detections:
            # 置信度过滤
            if detection['score'] < min_score:
                continue
            
            # 类别过滤
            if target_classes and detection['class'] not in target_classes:
                continue
            
            filtered.append(detection)
        
        logger.info(f"Filtered {len(detections)} -> {len(filtered)} detections")
        return filtered
    
    def sort_detections_by_position(self, detections: List[Dict], 
                                   reading_order: str = "top_to_bottom") -> List[Dict]:
        """
        按位置排序检测结果
        
        Args:
            detections: 检测结果列表
            reading_order: 阅读顺序 ("top_to_bottom", "left_to_right")
            
        Returns:
            排序后的检测结果
        """
        if reading_order == "top_to_bottom":
            # 按Y坐标排序（从上到下）
            sorted_detections = sorted(detections, key=lambda x: x['bbox'][1])
        elif reading_order == "left_to_right":
            # 按X坐标排序（从左到右）
            sorted_detections = sorted(detections, key=lambda x: x['bbox'][0])
        else:
            # 综合排序：先按Y坐标，再按X坐标
            sorted_detections = sorted(detections, key=lambda x: (x['bbox'][1], x['bbox'][0]))
        
        return sorted_detections
    
    def get_layout_statistics(self, detections: List[Dict]) -> Dict[str, Any]:
        """
        获取布局统计信息
        
        Args:
            detections: 检测结果列表
            
        Returns:
            统计信息字典
        """
        if not detections:
            return {}
        
        # 按类型统计
        class_counts = {}
        class_areas = {}
        
        for detection in detections:
            class_name = detection['class']
            area = detection['area']
            
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
            if class_name not in class_areas:
                class_areas[class_name] = []
            class_areas[class_name].append(area)
        
        # 计算统计信息
        statistics = {
            'total_objects': len(detections),
            'class_counts': class_counts,
            'class_areas': {
                class_name: {
                    'total': sum(areas),
                    'average': sum(areas) / len(areas),
                    'max': max(areas),
                    'min': min(areas)
                }
                for class_name, areas in class_areas.items()
            }
        }
        
        return statistics
