#!/usr/bin/env python3
"""
模型下载脚本
Model Download Script
"""
import os
import sys
import argparse
from pathlib import Path
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 模型配置
MODELS = {
    "qwen2.5-vl": {
        "repo": "Qwen/Qwen2.5-VL-7B-Instruct",
        "local_dir": "./models/Qwen2.5-VL-7B-Instruct",
        "description": "Qwen2.5 多模态视觉语言模型"
    },
    "qwen2.5": {
        "repo": "Qwen/Qwen2.5-7B-Instruct",
        "local_dir": "./models/Qwen2.5-7B-Instruct",
        "description": "Qwen2.5 语言模型"
    },
    "bge": {
        "repo": "BAAI/bge-large-zh-v1.5",
        "local_dir": "./models/bge-large-zh-v1.5",
        "description": "BGE 中文嵌入模型"
    }
}

def check_huggingface_cli():
    """检查huggingface-cli是否可用"""
    try:
        result = subprocess.run(["huggingface-cli", "--version"], 
                              capture_output=True, text=True, check=True)
        logger.info(f"huggingface-cli版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("huggingface-cli未安装或不可用")
        logger.info("请安装: pip install huggingface_hub[cli]")
        return False

def check_git_lfs():
    """检查git-lfs是否可用"""
    try:
        result = subprocess.run(["git", "lfs", "version"], 
                              capture_output=True, text=True, check=True)
        logger.info(f"git-lfs版本: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("git-lfs未安装，大文件下载可能失败")
        logger.info("请安装git-lfs: https://git-lfs.github.io/")
        return False

def download_model(model_key: str, force: bool = False):
    """
    下载指定模型
    
    Args:
        model_key: 模型键名
        force: 是否强制重新下载
    """
    if model_key not in MODELS:
        logger.error(f"未知模型: {model_key}")
        logger.info(f"可用模型: {list(MODELS.keys())}")
        return False
    
    model_info = MODELS[model_key]
    repo = model_info["repo"]
    local_dir = Path(model_info["local_dir"])
    description = model_info["description"]
    
    logger.info(f"开始下载: {description}")
    logger.info(f"仓库: {repo}")
    logger.info(f"本地目录: {local_dir}")
    
    # 检查是否已存在
    if local_dir.exists() and not force:
        logger.warning(f"模型已存在: {local_dir}")
        logger.info("使用 --force 强制重新下载")
        return True
    
    # 创建目录
    local_dir.parent.mkdir(parents=True, exist_ok=True)
    
    # 构建下载命令
    cmd = [
        "huggingface-cli", "download",
        repo,
        "--local-dir", str(local_dir),
        "--local-dir-use-symlinks", "False"
    ]
    
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, text=True)
        logger.info(f"✅ {description} 下载完成")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} 下载失败: {e}")
        return False
    except KeyboardInterrupt:
        logger.warning("下载被用户中断")
        return False

def download_all_models(force: bool = False):
    """下载所有模型"""
    logger.info("开始下载所有模型...")
    
    success_count = 0
    total_count = len(MODELS)
    
    for model_key in MODELS:
        logger.info(f"\n{'='*50}")
        logger.info(f"下载进度: {success_count + 1}/{total_count}")
        
        if download_model(model_key, force):
            success_count += 1
        else:
            logger.error(f"模型 {model_key} 下载失败")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"下载完成: {success_count}/{total_count} 个模型成功")
    
    if success_count == total_count:
        logger.info("🎉 所有模型下载成功！")
        return True
    else:
        logger.warning(f"⚠️  {total_count - success_count} 个模型下载失败")
        return False

def list_models():
    """列出所有可用模型"""
    logger.info("可用模型列表:")
    logger.info("=" * 60)
    
    for key, info in MODELS.items():
        status = "✅ 已下载" if Path(info["local_dir"]).exists() else "❌ 未下载"
        logger.info(f"模型: {key}")
        logger.info(f"  描述: {info['description']}")
        logger.info(f"  仓库: {info['repo']}")
        logger.info(f"  本地路径: {info['local_dir']}")
        logger.info(f"  状态: {status}")
        logger.info("-" * 40)

def check_models():
    """检查模型下载状态"""
    logger.info("检查模型状态...")
    
    all_downloaded = True
    for key, info in MODELS.items():
        local_dir = Path(info["local_dir"])
        if local_dir.exists():
            # 检查是否有模型文件
            model_files = list(local_dir.glob("*.bin")) + list(local_dir.glob("*.safetensors"))
            if model_files:
                logger.info(f"✅ {key}: 已下载 ({len(model_files)} 个模型文件)")
            else:
                logger.warning(f"⚠️  {key}: 目录存在但无模型文件")
                all_downloaded = False
        else:
            logger.error(f"❌ {key}: 未下载")
            all_downloaded = False
    
    if all_downloaded:
        logger.info("🎉 所有模型都已正确下载！")
    else:
        logger.warning("⚠️  部分模型缺失，请运行下载命令")
    
    return all_downloaded

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="工业智维助手模型下载工具")
    parser.add_argument("--model", choices=list(MODELS.keys()), 
                       help="下载指定模型")
    parser.add_argument("--all", action="store_true", 
                       help="下载所有模型")
    parser.add_argument("--list", action="store_true", 
                       help="列出所有可用模型")
    parser.add_argument("--check", action="store_true", 
                       help="检查模型下载状态")
    parser.add_argument("--force", action="store_true", 
                       help="强制重新下载")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_huggingface_cli():
        sys.exit(1)
    
    check_git_lfs()  # 警告但不退出
    
    if args.list:
        list_models()
    elif args.check:
        check_models()
    elif args.model:
        download_model(args.model, args.force)
    elif args.all:
        download_all_models(args.force)
    else:
        parser.print_help()
        logger.info("\n使用示例:")
        logger.info("  python download_models.py --list          # 列出所有模型")
        logger.info("  python download_models.py --check         # 检查模型状态")
        logger.info("  python download_models.py --model qwen2.5 # 下载指定模型")
        logger.info("  python download_models.py --all           # 下载所有模型")

if __name__ == "__main__":
    main()
