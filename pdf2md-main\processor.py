import os
from converters import PDFToImageConverter, ImageToMarkdownConverter

"""
整合 PDF 转图像和图像转 Markdown 的主处理器，支持断点续传
"""
class PDFMarkdownProcessor:
    """
    一个用于将 PDF 文件转换为 Markdown 文件的主处理器类。
    该类整合了 PDF 转图像和图像转 Markdown 的功能，支持将转换后的 Markdown 内容合并到一个文件中。
    """
    
    def __init__(self, pdf_converter=None, image_converter=None, md_output_dir="markdown_output"):
        """
        初始化 PDFMarkdownProcessor 类的实例。

        参数:
        pdf_converter (PDFToImageConverter, 可选): PDF 转图像的转换器实例。默认为 None，会创建一个新的 PDFToImageConverter 实例。
        image_converter (ImageToMarkdownConverter, 可选): 图像转 Markdown 的转换器实例。默认为 None，会创建一个新的 ImageToMarkdownConverter 实例。
        md_output_dir (str, 可选): 存储中间 Markdown 文件的目录。默认为 "markdown_output"。
        """
        # 若未提供 pdf_converter，则创建一个新的 PDFToImageConverter 实例
        self.pdf_converter = pdf_converter or PDFToImageConverter()
        # 若未提供 image_converter，则创建一个新的 ImageToMarkdownConverter 实例
        self.image_converter = image_converter or ImageToMarkdownConverter()
        self.md_output_dir = md_output_dir
        # 检查存储中间 Markdown 文件的目录是否存在，若不存在则创建
        if not os.path.exists(self.md_output_dir):
            os.makedirs(self.md_output_dir)
    
    def process(self, pdf_path, output_md_path="output.md"):
        """
        处理指定的 PDF 文件，将其转换为 Markdown 文件。

        参数:
        pdf_path (str): 要转换的 PDF 文件的路径。
        output_md_path (str, 可选): 最终合并后的 Markdown 文件的保存路径。默认为 "output.md"。

        返回:
        None
        """
        # 调用 PDF 转图像的转换器，将 PDF 文件转换为图像文件列表
        image_paths = self.pdf_converter.convert(pdf_path)
        # 用于存储每个页面转换后的 Markdown 文本
        markdown_texts = []
        
        # 遍历每个图像文件路径
        for i, image_path in enumerate(image_paths):
            # 计算当前页面的页码，从 1 开始
            page_num = i + 1
            # 生成当前页面 Markdown 文件的存储路径
            md_file = os.path.join(self.md_output_dir, f"page_{page_num}.md")
            # 调用图像转 Markdown 的转换器，将图像文件转换为 Markdown 文本
            markdown_text = self.image_converter.convert(image_path, md_file)
            
            # 检查转换结果是否有效
            if markdown_text.startswith("Conversion failed"):
                print(f"Warning: Page {page_num} failed to convert.")
                # 将包含错误信息的 Markdown 文本添加到列表中
                markdown_texts.append(f"## Page {page_num}\n{markdown_text}\n")
            else:
                # 将正常转换的 Markdown 文本添加到列表中
                markdown_texts.append(f"## Page {page_num}\n{markdown_text}\n")
        
        # 合并所有 Markdown 文本到最终文件
        with open(output_md_path, "w", encoding="utf-8") as f:
            f.write("\n".join(markdown_texts))
        print(f"Combined Markdown saved to: {output_md_path}")
