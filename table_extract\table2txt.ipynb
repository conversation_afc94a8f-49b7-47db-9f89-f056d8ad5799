{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-05 00:27:38,071 - modelscope - INFO - TensorFlow version 2.8.0 Found.\n", "2025-05-05 00:27:38,071 - modelscope - INFO - Loading ast index from C:\\Users\\<USER>\\.cache\\modelscope\\ast_indexer\n", "2025-05-05 00:27:38,331 - modelscope - INFO - Loading done! Current index file version is 1.4.0, with md5 4b980b2fb9f768029c79cab85aa1af1a and a total number of 842 components indexed\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 'torch'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 从 modelscope 库的 pipelines 模块导入 pipeline 函数，该函数用于快速创建预训练模型的推理管道\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmodelscope\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpipelines\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m pipeline\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# 从 modelscope 库的 utils.constant 模块导入 Tasks 类，该类定义了各种预定义的任务类型\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmodelscope\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconstant\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Tasks\n", "File \u001b[1;32me:\\Anaconda\\envs\\table2txt\\lib\\site-packages\\modelscope\\pipelines\\__init__.py:6\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmode<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimport_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m LazyImportModule\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m audio, cv, multi_modal, nlp\n\u001b[1;32m----> 6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Pipeline\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbuilder\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m pipeline\n", "File \u001b[1;32me:\\Anaconda\\envs\\table2txt\\lib\\site-packages\\modelscope\\pipelines\\base.py:27\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmodelscope\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mimport_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m is_tf_available, is_torch_available\n\u001b[0;32m     26\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmodelscope\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlogger\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_logger\n\u001b[1;32m---> 27\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmodelscope\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtorch_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m compile_model\n\u001b[0;32m     28\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m is_model, is_official_hub_path\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_torch_available():\n", "File \u001b[1;32me:\\Anaconda\\envs\\table2txt\\lib\\site-packages\\modelscope\\utils\\torch_utils.py:13\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Callable, List, Optional, Tuple\n\u001b[0;32m     12\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m---> 13\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[0;32m     14\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mtorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmultiprocessing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmp\u001b[39;00m\n\u001b[0;32m     15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mpackaging\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m version\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'torch'"]}], "source": ["# 从 modelscope 库的 pipelines 模块导入 pipeline 函数，该函数用于快速创建预训练模型的推理管道\n", "from modelscope.pipelines import pipeline\n", "# 从 modelscope 库的 utils.constant 模块导入 Tasks 类，该类定义了各种预定义的任务类型\n", "from modelscope.utils.constant import Tasks\n", "\n", "# 使用 pipeline 函数创建一个表格识别的推理管道\n", "# Tasks.table_recognition 指定任务类型为表格识别\n", "# model 参数指定使用的预训练模型的路径，这里使用 cv_dla34_table-structure-recognition_cycle-centernet 模型\n", "table_recognition = pipeline(Tasks.table_recognition, model='iic/cv_resnet-transformer_table-structure-recognition_lore')\n", "\n", "# 调用表格识别管道，传入需要进行表格识别的图片路径\n", "# 该函数会对指定路径的图片进行表格识别，并将识别结果存储在 result 变量中\n", "result = table_recognition('E:\\Pycharm\\AI-LLM\\llm_related-main\\table_extract\\imgs\\20250504235011.jpg')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从 paddleocr 库中导入 PaddleOCR 类，该类用于执行 OCR（光学字符识别）任务\n", "from paddleocr import PaddleOCR\n", "# 初始化 PaddleOCR 实例，设置使用 GPU 进行计算，并且指定识别语言为中文\n", "ocr = PaddleOCR(use_gpu=True, lang='ch')\n", "# 定义需要进行 OCR 识别的图片文件路径\n", "image_path = '你需要提取的图片路径'\n", "# 调用 ocr 实例的 ocr 方法对指定图片进行 OCR 识别，cls=True 表示开启方向分类功能\n", "res = ocr.ocr(image_path, cls=True)\n", "# 打印 OCR 识别结果\n", "print(res)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从 PIL 库导入 Image、ImageDraw 和 ImageFont 类，用于图像处理、绘制图形和设置字体\n", "from PIL import Image, ImageDraw, ImageFont\n", "# 导入 textwrap 模块，用于文本换行处理（当前代码未实际使用）\n", "import textwrap\n", "# 导入 numpy 库并别名为 np（当前代码未实际使用）\n", "import numpy as np\n", "\n", "def draw_ocr_boxes(image_path, boxes, texts):\n", "    \"\"\"\n", "    在指定图片上绘制 OCR 识别出的文本框和对应的文本，并保存处理后的图片。\n", "\n", "    Args:\n", "        image_path (str): 原始图片的文件路径。\n", "        boxes (list): 包含文本框坐标的列表，每个元素是一个表示文本框左上角和右下角坐标的元组。\n", "        texts (list): 包含每个文本框对应文本的列表。\n", "    \"\"\"\n", "    # 打开指定路径的图片\n", "    img = Image.open(image_path)\n", "    # 创建一个与原始图片大小相同的白色背景 RGB 图像\n", "    img = Image.new('RGB', img.size, (255, 255, 255))\n", "    \n", "    # 创建一个 ImageDraw 对象，用于在图像上绘制图形和文本\n", "    draw = ImageDraw.Draw(img)\n", "    # 加载指定的中文字体文件，设置字体大小为 15\n", "    font = ImageFont.truetype(\"./chinese_cht.ttf\", size=15)  \n", "\n", "    # 遍历每个文本框和对应的文本\n", "    for box, text in zip(boxes, texts):\n", "        # 在图像上绘制矩形框，边框颜色为红色，线宽为 2\n", "        draw.rectangle(box, outline='red', width=2)\n", "        # 提取文本框的左上角坐标\n", "        x, y = box[:2]\n", "        # 在矩形框左上角位置绘制文本，字体为之前加载的字体，颜色为黑色\n", "        draw.text((x, y), text, font=font, fill='black')\n", "    \n", "    # 将处理后的图像保存为 'image_with_boxes_and_text.jpg'\n", "    img.save('image_with_boxes_and_text.jpg')\n", "\n", "# 从 OCR 识别结果 res 中提取每个文本框的左上角和右下角坐标\n", "boxes = [(*i[0][0], *i[0][2]) for i in res[0]]\n", "# 从 OCR 识别结果 res 中提取每个文本框对应的文本\n", "texts = [i[1][0] for i in res[0]]\n", "# 调用 draw_ocr_boxes 函数，在指定图片上绘制文本框和文本\n", "draw_ocr_boxes('你需要提取的图片路径', boxes, texts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_inside_text(cell, text):\n", "    \"\"\"\n", "    检查文字对应的矩形框是否完全在单元格对应的矩形框内。\n", "\n", "    Args:\n", "        cell (tuple): 单元格的坐标，格式为 (x1, y1, x2, y2)。\n", "        text (dict): 包含文字信息的字典，其中 'coords' 键对应文字框的坐标，格式为 (x1, y1, x2, y2)。\n", "\n", "    Returns:\n", "        bool: 如果文字框完全在单元格框内返回 True，否则返回 False。\n", "    \"\"\"\n", "    # 提取单元格的左上角和右下角坐标\n", "    cx1, cy1, cx2, cy2 = cell\n", "    # 提取文字框的左上角和右下角坐标\n", "    tx1, ty1, tx2, ty2 = text['coords']\n", "    # 判断文字框是否完全在单元格框内\n", "    return cx1 <= tx1 and cy1 <= ty1 and cx2 >= tx2 and cy2 >= ty2\n", "\n", "def calculate_iou(cell, text):\n", "    \"\"\"\n", "    计算两个矩形框的交并比（IoU）。\n", "\n", "    Args:\n", "        cell (tuple): 单元格的坐标 (x1, y1, x2, y2)。\n", "        text (dict): 包含文本框坐标的字典，键为 'coords'，值为 (x1, y1, x2, y2)。\n", "\n", "    Returns:\n", "        float: 交并比（IoU）的值，范围在 0 到 1 之间。\n", "    \"\"\"\n", "    # 计算交集的左上角和右下角坐标\n", "    intersection_x1 = max(cell[0], text['coords'][0])\n", "    intersection_y1 = max(cell[1], text['coords'][1])\n", "    intersection_x2 = min(cell[2], text['coords'][2])\n", "    intersection_y2 = min(cell[3], text['coords'][3])\n", "\n", "    # 如果没有交集，返回 0\n", "    if intersection_x1 >= intersection_x2 or intersection_y1 >= intersection_y2:\n", "        return 0.0\n", "\n", "    # 计算交集的面积\n", "    intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)\n", "\n", "    # 计算单元格的面积\n", "    area_box1 = (cell[2] - cell[0]) * (cell[3] - cell[1])\n", "    # 计算文本框的面积\n", "    area_box2 = (text['coords'][2] - text['coords'][0]) * (text['coords'][3] - text['coords'][1])\n", "    # 计算并集的面积\n", "    union_area = area_box1 + area_box2 - intersection_area\n", "\n", "    # 计算 IoU\n", "    iou = intersection_area / union_area\n", "\n", "    return iou\n", "\n", "def calculate_iot(cell, text):\n", "    \"\"\"\n", "    计算两个矩形框的交集面积和文本框面积的比值（IoT）。\n", "\n", "    Args:\n", "        cell (tuple): 单元格的坐标 (x1, y1, x2, y2)。\n", "        text (dict): 包含文本框坐标的字典，键为 'coords'，值为 (x1, y1, x2, y2)。\n", "\n", "    Returns:\n", "        float: IoT 的值，范围在 0 到 1 之间。\n", "    \"\"\"\n", "    # 计算交集的左上角和右下角坐标\n", "    intersection_x1 = max(cell[0], text['coords'][0])\n", "    intersection_y1 = max(cell[1], text['coords'][1])\n", "    intersection_x2 = min(cell[2], text['coords'][2])\n", "    intersection_y2 = min(cell[3], text['coords'][3])\n", "\n", "    # 如果没有交集，返回 0\n", "    if intersection_x1 >= intersection_x2 or intersection_y1 >= intersection_y2:\n", "        return 0.0\n", "\n", "    # 计算交集的面积\n", "    intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)\n", "\n", "    # 计算文本框的面积\n", "    text_area = (text['coords'][2] - text['coords'][0]) * (text['coords'][3] - text['coords'][1])\n", "    # 计算 IoT\n", "    iot = intersection_area / text_area\n", "    return iot\n", "\n", "def merge_text_into_cells(cell_coords, ocr_results):\n", "    \"\"\"\n", "    将 OCR 识别出的文字合并到对应的单元格中。\n", "\n", "    Args:\n", "        cell_coords (list): 包含单元格坐标的列表，每个元素是一个元组 (x1, y1, x2, y2)。\n", "        ocr_results (list): 包含 OCR 识别结果的列表，每个元素是一个字典，包含 'text' 和 'coords' 键。\n", "\n", "    Returns:\n", "        dict: 合并后的字典，键为单元格坐标或非单元格文字框坐标，值为合并后的文字。\n", "    \"\"\"\n", "    # 创建一个字典，键是单元格坐标，值是属于该单元格的文字列表\n", "    cell_text_dict = {cell: [] for cell in cell_coords}\n", "    # 用于存储不在任何单元格内的文字\n", "    noncell_text_dict = {}\n", "\n", "    # 遍历每个单元格\n", "    for cell in cell_coords:\n", "        # 遍历 OCR 结果\n", "        for result in ocr_results:\n", "            # 如果文字框与单元格的 IoT 大于 0.5，则将文字添加到该单元格的文字列表中\n", "            if calculate_iot(cell, result) > 0.5:\n", "                cell_text_dict[cell].append(result['text'])\n", "\n", "    # 遍历 OCR 结果，找出不在任何单元格内的文字\n", "    for result in ocr_results:\n", "        if all(calculate_iot(cell, result) < 0.1 for cell in cell_coords):\n", "            noncell_text_dict[result['coords']] = result['text']\n", "\n", "    # 用于存储最终合并后的结果\n", "    merged_text = {}\n", "    # 合并单元格内的文字\n", "    for cell, texts in cell_text_dict.items():\n", "        merged_text[cell] = ''.join(texts).strip()\n", "    # 合并非单元格内的文字\n", "    for coords, text in noncell_text_dict.items():\n", "        merged_text[coords] = ''.join(text).strip()\n", "\n", "    return merged_text\n", "\n", "# 从表格识别结果中提取单元格坐标\n", "cell_coords = [tuple([*i[:2], *i[4:6]]) for i in result['polygons']]\n", "# 从 OCR 识别结果中提取文字和对应的坐标\n", "ocr_results = [\n", "    {'text': i[1][0], 'coords': tuple([*i[0][0], *i[0][2]])} for i in res[0]\n", "]\n", "# 调用函数将文字合并到单元格中\n", "merged_text = merge_text_into_cells(cell_coords, ocr_results)\n", "# 打印合并后的结果\n", "print(merged_text)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从 PIL 库导入 Image、ImageDraw 和 ImageFont 类，用于图像处理、绘制图形和设置字体\n", "from PIL import Image, ImageDraw, ImageFont\n", "# 导入 textwrap 模块，用于对长文本进行换行处理\n", "import textwrap\n", "# 导入 numpy 库并别名为 np，用于数值计算\n", "import numpy as np\n", "\n", "def draw_text_boxes(image_path, boxes, texts):\n", "    \"\"\"\n", "    在指定图片上绘制文本框，并将对应的文本添加到文本框内，若文本过长则进行换行处理。\n", "\n", "    Args:\n", "        image_path (str): 原始图片的文件路径。\n", "        boxes (list): 包含文本框坐标的列表，每个元素是一个表示文本框左上角和右下角坐标的元组。\n", "        texts (list): 包含每个文本框对应文本的列表。\n", "    \"\"\"\n", "    # 打开指定路径的图片\n", "    img = Image.open(image_path)\n", "    # 创建一个与原始图片大小相同的白色背景 RGB 图像\n", "    img = Image.new('RGB', img.size, (255, 255, 255))\n", "    # 创建一个 ImageDraw 对象，用于在图像上绘制图形和文本\n", "    draw = ImageDraw.Draw(img)\n", "    \n", "    # 设置字体，加载指定的中文字体文件，设置字体大小为 15\n", "    font = ImageFont.truetype(\"./chinese_cht.ttf\", size=15)  # 选择合适的字体和大小\n", "    \n", "\n", "    # 遍历每个文本框和对应的文本\n", "    for box, text in zip(boxes, texts):\n", "        # 在图像上绘制矩形文本框，边框颜色为红色，线宽为 2\n", "        draw.rectangle(box, outline='red', width=2)\n", "        \n", "        # 计算文本在当前字体下的边界框\n", "        text_len = draw.textbbox(xy=box[:2], text=text, font=font)\n", "        \n", "        if (text_len[2]-text_len[0]) > (box[2] - box[0]):\n", "            # 如果文本的实际宽度大于文本框的宽度，则将文本进行换行处理\n", "            # 计算每行可容纳的字符数，通过文本总长度和宽度比例计算\n", "            text = '\\n'.join(textwrap.wrap(text, width=int(np.ceil((len(text) / np.ceil((text_len[2]-text_len[0]) / (box[2] - box[0])))))))\n", "        else:\n", "            # 若文本宽度小于等于文本框宽度，则不做处理\n", "            text = text\n", "        # 提取文本框的左上角坐标\n", "        x, y = box[:2]\n", "        \n", "        # 在文本框左上角位置绘制文本，字体为之前加载的字体，颜色为黑色\n", "        # 注意：当前代码未实现真正的居中，仅从左上角开始绘制\n", "        draw.text((x,y), text, font=font, fill='black')\n", "    \n", "    # 将处理后的图像保存到指定路径\n", "    img.save('你保存的图片路径')\n", "\n", "# 示例：从 merged_text 字典中提取文本框坐标和对应的文字\n", "boxes = list(merged_text.keys())\n", "texts = list(merged_text.values())\n", "# 调用 draw_text_boxes 函数，在指定图片上绘制文本框和文本\n", "draw_text_boxes('你需要提取的图片路径', boxes, texts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def adjust_coordinates(merged_text, image_path):\n", "    \"\"\"\n", "    调整合并后文本框的坐标，将 y 坐标相近的文本框归为一组，并计算该组 y 坐标的平均值，\n", "    用平均值更新组内所有文本框的 y 坐标。\n", "\n", "    Args:\n", "        merged_text (dict): 合并后的文本和坐标字典，键为文本框坐标，值为对应的文本。\n", "        image_path (str): 图片文件的路径，用于获取图片的高度信息。\n", "\n", "    Returns:\n", "        dict: 调整后的文本和坐标字典，键为调整后的文本框坐标，值为对应的文本。\n", "    \"\"\"\n", "    # 打开指定路径的图片\n", "    image = Image.open(image_path)\n", "    # 获取图片的宽度和高度\n", "    width, height = image.size\n", "    # 计算 y 坐标分组的阈值，取图片高度的 1/100\n", "    threshold = height / 100\n", "    # 初始化分组字典，用于存储 y 坐标相近的文本框\n", "    groups = {}\n", "    \n", "    # 遍历合并后的文本和坐标字典\n", "    for coordinates, text in merged_text.items():\n", "        # 标记是否找到合适的分组\n", "        found_group = False\n", "        # 遍历已有的分组\n", "        for group_y in groups.keys():\n", "            # 判断当前文本框的 y 坐标与分组的 y 坐标差值是否在阈值范围内\n", "            if abs(coordinates[1] - group_y) <= threshold:\n", "                # 若在范围内，将当前文本框添加到该分组中\n", "                groups[group_y].append((coordinates, text))\n", "                # 标记已找到合适的分组\n", "                found_group = True\n", "                break\n", "\n", "        # 如果没有找到合适的分组，则创建一个新的分组\n", "        if not found_group:\n", "            groups[coordinates[1]] = [(coordinates, text)]\n", "    \n", "    # 初始化调整后的坐标字典\n", "    adjusted_coordinates = {}\n", "    # 遍历每个分组\n", "    for group_y, group_coords in groups.items():\n", "        # 计算该分组内所有文本框 y 坐标的平均值\n", "        avg_y = sum(coord[0][1] for coord in group_coords) / len(group_coords)\n", "        # 遍历分组内的每个文本框\n", "        for i in group_coords:\n", "            # 使用平均值更新文本框的 y 坐标，并存储到调整后的坐标字典中\n", "            adjusted_coordinates[(i[0][0], avg_y, i[0][2], i[0][3])] = i[1]\n", "        \n", "\n", "    return adjusted_coordinates\n", "\n", "# 调用函数处理坐标\n", "adjusted_merged_text = adjust_coordinates(merged_text, '你需要提取的图片路径')\n", "\n", "# 打印结果\n", "print(\"原始坐标:\", merged_text)\n", "print(\"调整后的坐标:\", adjusted_merged_text)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从 PIL 库导入 Image、ImageDraw 和 ImageFont 类，用于图像处理、绘制图形和设置字体\n", "from PIL import Image, ImageDraw, ImageFont\n", "# 导入 textwrap 模块，用于对长文本进行换行处理\n", "import textwrap\n", "# 导入 numpy 库并别名为 np，用于数值计算\n", "import numpy as np\n", "\n", "def draw_text_boxes(image_path, boxes, texts):\n", "    \"\"\"\n", "    在指定图片上绘制文本框，并将对应的文本添加到文本框内，若文本过长则进行换行处理。\n", "\n", "    Args:\n", "        image_path (str): 原始图片的文件路径。\n", "        boxes (list): 包含文本框坐标的列表，每个元素是一个表示文本框左上角和右下角坐标的元组。\n", "        texts (list): 包含每个文本框对应文本的列表。\n", "    \"\"\"\n", "    # 打开指定路径的图片\n", "    img = Image.open(image_path)\n", "    # 创建一个与原始图片大小相同的白色背景 RGB 图像\n", "    img = Image.new('RGB', img.size, (255, 255, 255))\n", "    # 创建一个 ImageDraw 对象，用于在图像上绘制图形和文本\n", "    draw = ImageDraw.Draw(img)\n", "    # 加载指定的中文字体文件，设置字体大小为 15\n", "    font = ImageFont.truetype(\"./chinese_cht.ttf\", size=15)  # 选择合适的字体和大小\n", "\n", "    # 遍历每个文本框和对应的文本\n", "    for box, text in zip(boxes, texts):\n", "        # 在图像上绘制矩形文本框，边框颜色为红色，线宽为 2\n", "        draw.rectangle(box, outline='red', width=2)\n", "\n", "        # 计算文本在当前字体下从文本框左上角开始绘制时的边界框\n", "        text_len = draw.textbbox(xy=box[:2], text=text, font=font)\n", "\n", "        if (text_len[2] - text_len[0]) > (box[2] - box[0]):\n", "            # 如果文本的实际宽度大于文本框的宽度，则将文本进行换行处理\n", "            # 计算每行可容纳的字符数，通过文本总长度和宽度比例计算\n", "            text = '\\n'.join(textwrap.wrap(text, width=int(np.ceil(len(text) / np.ceil((text_len[2] - text_len[0]) / (box[2] - box[0]))))))\n", "        else:\n", "            # 若文本宽度小于等于文本框宽度，则不做处理\n", "            text = text\n", "\n", "        # 提取文本框的左上角坐标\n", "        x, y = box[:2]\n", "        # 在文本框左上角位置绘制文本，字体为之前加载的字体，颜色为黑色\n", "        draw.text((x, y), text, font=font, fill='black')\n", "\n", "    # 将处理后的图像保存到指定路径\n", "    img.save('你需要保存的图片路径')\n", "\n", "# 从 adjusted_merged_text 字典中提取文本框坐标并转换为列表\n", "boxes = list(adjusted_merged_text.keys())\n", "# 从 adjusted_merged_text 字典中提取对应的文字并转换为列表\n", "texts = list(adjusted_merged_text.values())\n", "# 调用 draw_text_boxes 函数，在指定图片上绘制文本框和文本\n", "draw_text_boxes('你需要提取的图片路径', boxes, texts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 输出最终的文本\n", "# 对调整后的文本和坐标字典进行排序，先按 y 坐标排序，若 y 坐标相同则按 x 坐标排序\n", "# sorted 函数返回一个包含元组的列表，每个元组由坐标和对应的文本组成\n", "adjusted_merged_text_sorted = sorted(adjusted_merged_text.items(), key=lambda x: (x[0][1], x[0][0]))\n", "# 初始化一个空字典，用于按 y 坐标分组存储文本\n", "adjusted_merged_text_sorted_group = {}\n", "# 遍历排序后的文本和坐标列表\n", "for coordinates, text in adjusted_merged_text_sorted:\n", "    # 提取当前文本框的 y 坐标\n", "    y_coord = coordinates[1]\n", "    # 检查该 y 坐标是否已经在分组字典的键中\n", "    if y_coord not in adjusted_merged_text_sorted_group:\n", "        # 若不在，以该 y 坐标为键，创建一个包含当前文本的列表作为值\n", "        adjusted_merged_text_sorted_group[y_coord] = [text]\n", "    else:\n", "        # 若在，将当前文本添加到对应 y 坐标的列表中\n", "        adjusted_merged_text_sorted_group[y_coord].append(text)\n", "# 遍历分组字典中每个 y 坐标对应的文本列表\n", "for text_list in adjusted_merged_text_sorted_group.values():\n", "    # 将每个文本列表中的文本用 ' | ' 连接成字符串并打印输出\n", "    print(' | '.join(text_list))\n"]}], "metadata": {"kernelspec": {"display_name": "table2txt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}