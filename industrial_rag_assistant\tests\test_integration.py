"""
集成测试
"""
import pytest
import sys
import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from main import IndustrialRAGAssistant
from config.settings import settings

class TestIndustrialRAGAssistantIntegration:
    """工业RAG助手集成测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    @pytest.fixture
    def mock_assistant(self):
        """创建模拟的RAG助手实例"""
        with patch('main.PDFParser'), \
             patch('main.KnowledgeBaseBuilder'), \
             patch('main.RetrievalGenerator'):
            assistant = IndustrialRAGAssistant()
            return assistant
    
    def test_assistant_initialization(self, mock_assistant):
        """测试助手初始化"""
        assert mock_assistant is not None
        assert hasattr(mock_assistant, 'pdf_parser')
        assert hasattr(mock_assistant, 'kb_builder')
        assert hasattr(mock_assistant, 'retrieval_generator')
    
    def test_process_pdf_mock(self, mock_assistant, temp_dir):
        """测试PDF处理（模拟）"""
        # 创建模拟PDF文件
        pdf_path = temp_dir / "test.pdf"
        pdf_path.write_text("mock pdf content")
        
        # 模拟PDF解析结果
        mock_result = {
            "pages": [
                {
                    "page_num": 1,
                    "regions": [
                        {
                            "type": "text",
                            "content": "这是测试文档内容",
                            "bbox": [0, 0, 100, 100]
                        }
                    ]
                }
            ],
            "total_pages": 1,
            "processing_time": 1.0
        }
        
        mock_assistant.pdf_parser.parse_pdf.return_value = mock_result
        
        # 测试处理
        result = mock_assistant.process_pdf(str(pdf_path))
        
        assert result is not None
        assert "pages" in result
        assert result["total_pages"] == 1
        mock_assistant.pdf_parser.parse_pdf.assert_called_once()
    
    def test_build_knowledge_base_mock(self, mock_assistant):
        """测试知识库构建（模拟）"""
        # 模拟文档数据
        documents = [
            {
                "content": "这是第一个文档",
                "metadata": {"source": "doc1.pdf", "page": 1}
            },
            {
                "content": "这是第二个文档",
                "metadata": {"source": "doc2.pdf", "page": 1}
            }
        ]
        
        # 模拟知识库构建结果
        mock_result = {
            "vector_store": Mock(),
            "bm25_retriever": Mock(),
            "document_count": 2,
            "embedding_dimension": 1024
        }
        
        mock_assistant.kb_builder.build_knowledge_base.return_value = mock_result
        
        # 测试构建
        result = mock_assistant.build_knowledge_base(documents)
        
        assert result is not None
        assert "document_count" in result
        assert result["document_count"] == 2
        mock_assistant.kb_builder.build_knowledge_base.assert_called_once()
    
    def test_query_mock(self, mock_assistant):
        """测试查询（模拟）"""
        # 模拟查询结果
        mock_answer = {
            "answer": "这是一个测试回答",
            "sources": ["doc1.pdf", "doc2.pdf"],
            "confidence": 0.85,
            "retrieved_docs": [
                {"content": "相关文档1", "score": 0.9},
                {"content": "相关文档2", "score": 0.8}
            ]
        }
        
        mock_assistant.retrieval_generator.generate_answer.return_value = mock_answer
        
        # 测试查询
        query = "什么是工业设备维护？"
        result = mock_assistant.query(query, Mock(), Mock())
        
        assert result is not None
        assert "answer" in result
        assert "sources" in result
        assert result["confidence"] > 0
        mock_assistant.retrieval_generator.generate_answer.assert_called_once()

class TestWorkflowIntegration:
    """工作流集成测试类"""
    
    def test_text_processing_workflow(self):
        """测试文本处理工作流"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试完整的文本处理流程
        text = """
        # 工业设备维护手册
        
        ## 第一章 概述
        本手册介绍了工业设备的维护方法和注意事项。
        
        ### 1.1 设备分类
        - 机械设备
        - 电气设备
        - 控制系统
        
        ## 第二章 维护流程
        设备维护包括以下步骤：
        1. 设备检查
        2. 故障诊断
        3. 维修处理
        4. 测试验证
        """
        
        metadata = {"source": "maintenance_manual.md", "type": "manual"}
        
        # 测试Markdown分割
        docs = processor.split_markdown_by_sections(text, metadata)
        
        assert len(docs) >= 2  # 至少有两个章节
        
        # 检查文档内容
        content_text = ' '.join([doc.page_content for doc in docs])
        assert "工业设备维护" in content_text
        assert "维护流程" in content_text
        
        # 测试BM25预处理
        for doc in docs:
            processed = processor.preprocess_for_bm25(doc.page_content)
            assert isinstance(processed, list)
            if processed:  # 如果有内容
                assert all(isinstance(token, str) for token in processed)
    
    def test_configuration_integration(self):
        """测试配置集成"""
        from config.settings import settings
        
        # 测试配置的一致性
        assert settings.CHUNK_SIZE > 0
        assert settings.CHUNK_OVERLAP < settings.CHUNK_SIZE
        assert settings.TOP_K > 0
        assert 0 <= settings.TEMPERATURE <= 2.0
        assert 0 <= settings.TOP_P <= 1.0
        
        # 测试路径配置
        assert settings.PROJECT_ROOT.exists()
        assert settings.DATA_DIR.name == "data"
        assert settings.MODELS_DIR.name == "models"
        
        # 测试提示词模板
        assert "{context}" in settings.USER_PROMPT_TEMPLATE
        assert "{question}" in settings.USER_PROMPT_TEMPLATE
    
    def test_error_handling(self):
        """测试错误处理"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试空文本处理
        empty_docs = processor.split_text("", {"source": "empty"})
        assert isinstance(empty_docs, list)
        
        # 测试无效输入
        invalid_processed = processor.preprocess_for_bm25(None)
        assert invalid_processed == []
        
        # 测试特殊字符
        special_text = "!@#$%^&*()"
        special_processed = processor.preprocess_for_bm25(special_text)
        assert isinstance(special_processed, list)

class TestSystemRequirements:
    """系统需求测试类"""
    
    def test_python_version(self):
        """测试Python版本"""
        import sys
        version = sys.version_info
        assert version.major == 3
        assert version.minor >= 8  # 要求Python 3.8+
    
    def test_required_packages(self):
        """测试必需的包"""
        required_packages = [
            'jieba',
            'cv2',
            'pydantic',
            'langchain',
            'pytest'
        ]
        
        for package in required_packages:
            try:
                if package == 'cv2':
                    import cv2
                else:
                    __import__(package)
            except ImportError:
                pytest.fail(f"Required package {package} not installed")
    
    def test_directory_structure(self):
        """测试目录结构"""
        project_root = Path(__file__).parent.parent
        
        required_dirs = [
            "config",
            "core",
            "utils",
            "tests",
            "data",
            "models"
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            assert dir_path.exists(), f"Required directory {dir_name} not found"
    
    def test_configuration_files(self):
        """测试配置文件"""
        project_root = Path(__file__).parent.parent
        
        required_files = [
            "config/settings.py",
            "main.py",
            "requirements.txt",
            "setup.py",
            "README.md"
        ]
        
        for file_path in required_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"Required file {file_path} not found"
            assert full_path.stat().st_size > 0, f"File {file_path} is empty"

if __name__ == "__main__":
    # 运行集成测试
    pytest.main([__file__, "-v", "--tb=short"])
