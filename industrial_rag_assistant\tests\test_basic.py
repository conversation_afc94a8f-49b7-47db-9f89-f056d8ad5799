"""
基础功能测试
"""
import pytest
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from utils.text_processor import TextProcessor
from utils.layout_analyzer import LayoutAnalyzer

class TestBasicFunctionality:
    """基础功能测试类"""
    
    def test_settings_import(self):
        """测试配置导入"""
        assert settings is not None
        assert hasattr(settings, 'CHUNK_SIZE')
        assert hasattr(settings, 'TOP_K')
        assert hasattr(settings, 'DEVICE')
    
    def test_text_processor_init(self):
        """测试文本处理器初始化"""
        processor = TextProcessor()
        assert processor is not None
        assert hasattr(processor, 'text_splitter')
    
    def test_text_preprocessing(self):
        """测试文本预处理"""
        processor = TextProcessor()
        
        # 测试中文文本
        text = "这是一个测试文本，包含标点符号！"
        processed = processor.preprocess_for_bm25(text)
        
        assert isinstance(processed, list)
        assert len(processed) > 0
        assert "测试" in processed
        assert "文本" in processed
    
    def test_text_splitting(self):
        """测试文本分割"""
        processor = TextProcessor()
        
        # 创建长文本
        long_text = "这是一个很长的文本。" * 100
        metadata = {"source": "test"}
        
        docs = processor.split_text(long_text, metadata)
        
        assert isinstance(docs, list)
        assert len(docs) > 0
        
        # 检查文档结构
        for doc in docs:
            assert hasattr(doc, 'page_content')
            assert hasattr(doc, 'metadata')
            assert doc.metadata['source'] == 'test'
    
    def test_markdown_section_splitting(self):
        """测试Markdown章节分割"""
        processor = TextProcessor()
        
        markdown_text = """
# 第一章 介绍
这是第一章的内容。

## 1.1 概述
这是概述部分。

# 第二章 详细说明
这是第二章的内容。

## 2.1 技术细节
这是技术细节。
"""
        
        metadata = {"source": "test.md"}
        docs = processor.split_markdown_by_sections(markdown_text, metadata)
        
        assert isinstance(docs, list)
        assert len(docs) >= 2  # 至少应该有两个章节
        
        # 检查是否包含章节标题
        content_text = ' '.join([doc.page_content for doc in docs])
        assert "第一章" in content_text
        assert "第二章" in content_text
    
    def test_layout_analyzer_init(self):
        """测试布局分析器初始化"""
        try:
            analyzer = LayoutAnalyzer()
            assert analyzer is not None
        except Exception as e:
            # 如果模型未安装，跳过测试
            pytest.skip(f"Layout analyzer initialization failed: {e}")
    
    def test_stopwords_filtering(self):
        """测试停用词过滤"""
        processor = TextProcessor()
        
        # 测试包含停用词的文本
        text = "这是一个的测试在文本中"
        processed = processor.preprocess_for_bm25(text)
        
        # 检查停用词是否被过滤
        assert "的" not in processed
        assert "在" not in processed
        assert "测试" in processed
        assert "文本" in processed
    
    def test_empty_text_handling(self):
        """测试空文本处理"""
        processor = TextProcessor()
        
        # 测试空文本
        empty_processed = processor.preprocess_for_bm25("")
        assert empty_processed == []
        
        # 测试只有空格的文本
        space_processed = processor.preprocess_for_bm25("   ")
        assert space_processed == []
        
        # 测试只有标点符号的文本
        punct_processed = processor.preprocess_for_bm25("！@#￥%……&*（）")
        assert punct_processed == []

class TestConfigurationValidation:
    """配置验证测试类"""
    
    def test_required_settings(self):
        """测试必需的配置项"""
        required_attrs = [
            'CHUNK_SIZE', 'CHUNK_OVERLAP', 'TOP_K', 'TEMPERATURE',
            'TOP_P', 'MAX_NEW_TOKENS', 'DEVICE', 'PDF_DPI'
        ]

        for attr in required_attrs:
            assert hasattr(settings, attr), f"Missing required setting: {attr}"
            assert getattr(settings, attr) is not None, f"Setting {attr} is None"

    def test_numeric_settings_range(self):
        """测试数值配置的合理范围"""
        assert 0 < settings.CHUNK_SIZE <= 2000
        assert 0 <= settings.CHUNK_OVERLAP < settings.CHUNK_SIZE
        assert 1 <= settings.TOP_K <= 20
        assert 0.0 <= settings.TEMPERATURE <= 2.0
        assert 0.0 <= settings.TOP_P <= 1.0
        assert 1 <= settings.MAX_NEW_TOKENS <= 4096
        assert settings.PDF_DPI >= 150

    def test_prompt_templates(self):
        """测试提示词模板"""
        assert hasattr(settings, 'USER_PROMPT_TEMPLATE')
        assert isinstance(settings.USER_PROMPT_TEMPLATE, str)
        assert len(settings.USER_PROMPT_TEMPLATE) > 0

        # 检查模板是否包含必要的占位符
        template = settings.USER_PROMPT_TEMPLATE
        assert "{context}" in template
        assert "{question}" in template

class TestUtilityFunctions:
    """工具函数测试类"""
    
    def test_chinese_text_processing(self):
        """测试中文文本处理"""
        processor = TextProcessor()
        
        # 测试混合中英文
        mixed_text = "这是中文text和English混合的内容123"
        processed = processor.preprocess_for_bm25(mixed_text)
        
        assert "中文" in processed
        assert "text" in processed
        assert "English" in processed
        assert "混合" in processed
        assert "内容" in processed
    
    def test_special_characters_handling(self):
        """测试特殊字符处理"""
        processor = TextProcessor()
        
        # 测试包含特殊字符的文本
        special_text = "测试@#$%^&*()文本"
        processed = processor.preprocess_for_bm25(special_text)
        
        assert "测试" in processed
        assert "文本" in processed
        # 特殊字符应该被过滤掉
        for char in "@#$%^&*()":
            assert char not in ' '.join(processed)
    
    def test_document_metadata_preservation(self):
        """测试文档元数据保持"""
        processor = TextProcessor()
        
        text = "这是测试文档内容"
        metadata = {
            "source": "test.pdf",
            "page": 1,
            "type": "text"
        }
        
        docs = processor.split_text(text, metadata)
        
        for doc in docs:
            assert doc.metadata["source"] == "test.pdf"
            assert doc.metadata["page"] == 1
            assert doc.metadata["type"] == "text"

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
