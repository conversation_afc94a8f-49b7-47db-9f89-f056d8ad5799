"""
多模态处理器 - 基于Qwen2.5-VL的多模态理解和内容转换
"""
import torch
from typing import List, Dict, Any, Optional, Union
from PIL import Image
import logging
from pathlib import Path

try:
    from transformers import AutoProcessor
    from vllm import LLM, SamplingParams
    from qwen_vl_utils import process_vision_info
except ImportError:
    logging.warning("Required packages not installed. Please install transformers, vllm, qwen_vl_utils")
    AutoProcessor = None
    LLM = None
    SamplingParams = None
    process_vision_info = None

try:
    from ..config.settings import settings
except ImportError:
    from config.settings import settings

logger = logging.getLogger(__name__)

class MultimodalProcessor:
    """多模态处理器"""
    
    def __init__(self, model_path: str = None, use_vllm: bool = True):
        """
        初始化多模态处理器
        
        Args:
            model_path: 模型路径
            use_vllm: 是否使用vLLM加速
        """
        self.model_path = model_path or settings.MULTIMODAL_MODEL_PATH
        self.use_vllm = use_vllm
        self.device = settings.DEVICE
        
        if not Path(self.model_path).exists():
            raise FileNotFoundError(f"Model not found: {self.model_path}")
        
        # 初始化模型和处理器
        self._initialize_model()
        
        logger.info(f"Multimodal processor initialized with model: {self.model_path}")
    
    def _initialize_model(self):
        """初始化模型和处理器"""
        try:
            if self.use_vllm and LLM is not None:
                # 使用vLLM加速推理
                self.llm = LLM(
                    model=self.model_path,
                    trust_remote_code=True,
                    max_model_len=8192,
                    gpu_memory_utilization=0.8
                )
                self.sampling_params = SamplingParams(
                    temperature=settings.TEMPERATURE,
                    top_p=settings.TOP_P,
                    max_tokens=settings.MAX_NEW_TOKENS
                )
                logger.info("vLLM model loaded successfully")
            else:
                # 使用transformers
                from transformers import Qwen2VLForConditionalGeneration
                
                self.model = Qwen2VLForConditionalGeneration.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.bfloat16,
                    device_map="auto",
                    trust_remote_code=True
                )
                logger.info("Transformers model loaded successfully")
            
            # 初始化处理器
            if AutoProcessor is not None:
                self.processor = AutoProcessor.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )
                logger.info("Processor loaded successfully")
            else:
                raise ImportError("AutoProcessor not available")
                
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    def process_image_to_markdown(self, image: Union[Image.Image, str], 
                                 content_type: str = "general") -> str:
        """
        将图像内容转换为Markdown格式
        
        Args:
            image: PIL图像对象或图像路径
            content_type: 内容类型 ("general", "table", "figure", "text")
            
        Returns:
            Markdown格式的内容
        """
        try:
            # 根据内容类型选择合适的提示词
            prompt = self._get_prompt_by_type(content_type)
            
            # 处理图像
            if isinstance(image, str):
                image = Image.open(image)
            
            # 生成内容
            if self.use_vllm and hasattr(self, 'llm'):
                markdown_content = self._generate_with_vllm(image, prompt)
            else:
                markdown_content = self._generate_with_transformers(image, prompt)
            
            # 后处理
            markdown_content = self._post_process_markdown(markdown_content)
            
            logger.info(f"Image to markdown conversion completed for {content_type}")
            return markdown_content
            
        except Exception as e:
            logger.error(f"Image to markdown conversion failed: {e}")
            return f"<!-- 图像处理失败: {str(e)} -->"
    
    def _get_prompt_by_type(self, content_type: str) -> str:
        """
        根据内容类型获取提示词
        
        Args:
            content_type: 内容类型
            
        Returns:
            提示词
        """
        prompts = {
            "table": settings.TABLE_EXTRACTION_PROMPT,
            "figure": settings.IMAGE_DESCRIPTION_PROMPT,
            "text": settings.TEXT_EXTRACTION_PROMPT,
            "general": settings.IMAGE_DESCRIPTION_PROMPT
        }
        
        return prompts.get(content_type, prompts["general"])
    
    def _generate_with_vllm(self, image: Image.Image, prompt: str) -> str:
        """
        使用vLLM生成内容
        
        Args:
            image: PIL图像对象
            prompt: 提示词
            
        Returns:
            生成的内容
        """
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理视觉信息
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 使用vLLM生成
            outputs = self.llm.generate(
                [text],
                sampling_params=self.sampling_params,
                images=image_inputs
            )
            
            return outputs[0].outputs[0].text
            
        except Exception as e:
            logger.error(f"vLLM generation failed: {e}")
            raise
    
    def _generate_with_transformers(self, image: Image.Image, prompt: str) -> str:
        """
        使用transformers生成内容
        
        Args:
            image: PIL图像对象
            prompt: 提示词
            
        Returns:
            生成的内容
        """
        try:
            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # 应用聊天模板
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 处理视觉信息
            image_inputs, video_inputs = process_vision_info(messages)
            
            # 处理输入
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = inputs.to(self.model.device)
            
            # 生成
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=settings.MAX_NEW_TOKENS,
                    temperature=settings.TEMPERATURE,
                    top_p=settings.TOP_P,
                    do_sample=True
                )
            
            # 解码
            generated_ids_trimmed = [
                out_ids[len(in_ids):] 
                for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            return output_text[0] if output_text else ""
            
        except Exception as e:
            logger.error(f"Transformers generation failed: {e}")
            raise
    
    def _post_process_markdown(self, content: str) -> str:
        """
        后处理Markdown内容
        
        Args:
            content: 原始内容
            
        Returns:
            处理后的内容
        """
        if not content:
            return ""
        
        # 移除可能的markdown代码块标记
        if '```markdown' in content:
            content = content.replace('```markdown\n', '')
            last_backticks_pos = content.rfind('```')
            if last_backticks_pos != -1:
                content = content[:last_backticks_pos]
        
        # 清理多余的空行
        lines = content.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            if not (is_empty and prev_empty):
                cleaned_lines.append(line)
            prev_empty = is_empty
        
        return '\n'.join(cleaned_lines).strip()
    
    def batch_process_images(self, images: List[Union[Image.Image, str]], 
                           content_types: List[str] = None) -> List[str]:
        """
        批量处理图像
        
        Args:
            images: 图像列表
            content_types: 内容类型列表
            
        Returns:
            Markdown内容列表
        """
        if content_types is None:
            content_types = ["general"] * len(images)
        
        if len(content_types) != len(images):
            content_types = content_types * len(images)
            content_types = content_types[:len(images)]
        
        results = []
        for i, (image, content_type) in enumerate(zip(images, content_types)):
            logger.info(f"Processing image {i+1}/{len(images)}")
            try:
                result = self.process_image_to_markdown(image, content_type)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process image {i+1}: {e}")
                results.append(f"<!-- 图像 {i+1} 处理失败 -->")
        
        return results
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            if hasattr(self, 'model'):
                del self.model
            if hasattr(self, 'llm'):
                del self.llm
            if hasattr(self, 'processor'):
                del self.processor
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info("Resources cleaned up successfully")
            
        except Exception as e:
            logger.warning(f"Resource cleanup failed: {e}")
