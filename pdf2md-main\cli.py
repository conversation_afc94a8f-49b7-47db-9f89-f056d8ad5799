import argparse

"""
解析命令行参数
"""
def parse_args():
    # 创建一个 ArgumentParser 对象，用于解析命令行参数
    # description 参数用于提供程序的描述信息，会在帮助信息中显示
    parser = argparse.ArgumentParser(description="Convert PDF to Markdown using Qwen2.5-VL-3B-Instruct")
    # 添加一个名为 --pdf 的命令行参数
    # type 指定参数类型为字符串
    # required=True 表示该参数是必需的，用户必须提供
    # help 提供该参数的帮助信息，会在帮助信息中显示
    parser.add_argument(
        "--pdf",
        type=str,
        required=True,
        help="Path to the input PDF file"
    )
    # 添加一个名为 --output 的命令行参数
    # type 指定参数类型为字符串
    # default 设置该参数的默认值，当用户未提供该参数时使用
    # help 提供该参数的帮助信息，会在帮助信息中显示
    parser.add_argument(
        "--output",
        type=str,
        default="output.md",
        help="Path to the output Markdown file (default: output.md)"
    )
    # 添加一个名为 --dpi 的命令行参数
    # type 指定参数类型为整数
    # default 设置该参数的默认值，当用户未提供该参数时使用
    # help 提供该参数的帮助信息，会在帮助信息中显示
    parser.add_argument(
        "--dpi",
        type=int,
        default=200,
        help="DPI for PDF to image conversion (default: 200)"
    )
    # 添加一个名为 --model 的命令行参数
    # type 指定参数类型为字符串
    # default 设置该参数的默认值，当用户未提供该参数时使用
    # help 提供该参数的帮助信息，会在帮助信息中显示
    parser.add_argument(
        "--model",
        type=str,
        default="Qwen/Qwen2.5-VL-3B-Instruct",
        help="Model name or path (default: Qwen/Qwen2.5-VL-3B-Instruct)"
    )
    # 解析命令行参数并返回解析结果
    return parser.parse_args()
