"""
OCR处理器 - 基于PaddleOCR的文字识别
"""
import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from PIL import Image
import logging

try:
    from paddleocr import PaddleOCR
except ImportError:
    logging.warning("PaddleOCR not installed. Please install it for OCR functionality.")
    PaddleOCR = None

try:
    from ..config.settings import settings
except ImportError:
    from config.settings import settings

logger = logging.getLogger(__name__)

class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self, use_gpu: bool = None, lang: str = None):
        """
        初始化OCR处理器
        
        Args:
            use_gpu: 是否使用GPU
            lang: 识别语言
        """
        self.use_gpu = use_gpu if use_gpu is not None else settings.OCR_USE_GPU
        self.lang = lang or settings.OCR_LANG
        
        if PaddleOCR is None:
            raise ImportError("PaddleOCR is required for text recognition")
        
        try:
            self.ocr = PaddleOCR(use_gpu=self.use_gpu, lang=self.lang)
            logger.info(f"OCR processor initialized with GPU: {self.use_gpu}, Lang: {self.lang}")
        except Exception as e:
            logger.error(f"Failed to initialize OCR processor: {e}")
            raise
    
    def recognize_text(self, image: Image.Image, cls: bool = True) -> List[Dict[str, Any]]:
        """
        识别图像中的文字
        
        Args:
            image: PIL图像对象
            cls: 是否开启方向分类
            
        Returns:
            OCR识别结果列表
        """
        try:
            # 转换为numpy数组
            if isinstance(image, Image.Image):
                image_array = np.array(image)
            else:
                image_array = image
            
            # 执行OCR识别
            ocr_results = self.ocr.ocr(image_array, cls=cls)
            
            # 处理识别结果
            processed_results = []
            
            if ocr_results and ocr_results[0]:
                for line_result in ocr_results[0]:
                    if line_result:
                        bbox = line_result[0]  # 边界框坐标
                        text_info = line_result[1]  # (文本, 置信度)
                        
                        # 计算边界框信息
                        x_coords = [point[0] for point in bbox]
                        y_coords = [point[1] for point in bbox]
                        
                        x1, y1 = min(x_coords), min(y_coords)
                        x2, y2 = max(x_coords), max(y_coords)
                        
                        processed_result = {
                            'text': text_info[0],
                            'confidence': float(text_info[1]),
                            'bbox': [x1, y1, x2, y2],  # [x1, y1, x2, y2]
                            'polygon': bbox,  # 原始多边形坐标
                            'center': [(x1 + x2) / 2, (y1 + y2) / 2],
                            'width': x2 - x1,
                            'height': y2 - y1,
                            'area': (x2 - x1) * (y2 - y1)
                        }
                        
                        processed_results.append(processed_result)
            
            logger.info(f"OCR completed: {len(processed_results)} text regions detected")
            return processed_results
            
        except Exception as e:
            logger.error(f"OCR recognition failed: {e}")
            raise
    
    def filter_text_results(self, ocr_results: List[Dict[str, Any]], 
                           min_confidence: float = 0.5,
                           min_text_length: int = 1) -> List[Dict[str, Any]]:
        """
        过滤OCR结果
        
        Args:
            ocr_results: OCR识别结果
            min_confidence: 最小置信度
            min_text_length: 最小文本长度
            
        Returns:
            过滤后的OCR结果
        """
        filtered_results = []
        
        for result in ocr_results:
            # 置信度过滤
            if result['confidence'] < min_confidence:
                continue
            
            # 文本长度过滤
            if len(result['text'].strip()) < min_text_length:
                continue
            
            # 去除纯空白文本
            if not result['text'].strip():
                continue
            
            filtered_results.append(result)
        
        logger.info(f"Filtered OCR results: {len(ocr_results)} -> {len(filtered_results)}")
        return filtered_results
    
    def merge_nearby_text(self, ocr_results: List[Dict[str, Any]], 
                         distance_threshold: float = 10.0) -> List[Dict[str, Any]]:
        """
        合并相邻的文本
        
        Args:
            ocr_results: OCR识别结果
            distance_threshold: 距离阈值
            
        Returns:
            合并后的OCR结果
        """
        if not ocr_results:
            return []
        
        merged_results = []
        used_indices = set()
        
        for i, result in enumerate(ocr_results):
            if i in used_indices:
                continue
            
            # 查找相邻的文本
            nearby_texts = [result]
            used_indices.add(i)
            
            for j, other_result in enumerate(ocr_results):
                if j in used_indices or i == j:
                    continue
                
                # 计算距离
                distance = self._calculate_text_distance(result, other_result)
                
                if distance < distance_threshold:
                    nearby_texts.append(other_result)
                    used_indices.add(j)
            
            # 合并相邻文本
            if len(nearby_texts) > 1:
                merged_result = self._merge_text_regions(nearby_texts)
            else:
                merged_result = result
            
            merged_results.append(merged_result)
        
        logger.info(f"Merged nearby text: {len(ocr_results)} -> {len(merged_results)}")
        return merged_results
    
    def _calculate_text_distance(self, text1: Dict[str, Any], text2: Dict[str, Any]) -> float:
        """
        计算两个文本区域的距离
        
        Args:
            text1: 第一个文本区域
            text2: 第二个文本区域
            
        Returns:
            距离值
        """
        center1 = text1['center']
        center2 = text2['center']
        
        return ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5
    
    def _merge_text_regions(self, text_regions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合并多个文本区域
        
        Args:
            text_regions: 文本区域列表
            
        Returns:
            合并后的文本区域
        """
        # 按位置排序（从左到右，从上到下）
        sorted_regions = sorted(text_regions, key=lambda x: (x['center'][1], x['center'][0]))
        
        # 合并文本
        merged_text = ' '.join([region['text'] for region in sorted_regions])
        
        # 计算合并后的边界框
        all_x1 = [region['bbox'][0] for region in text_regions]
        all_y1 = [region['bbox'][1] for region in text_regions]
        all_x2 = [region['bbox'][2] for region in text_regions]
        all_y2 = [region['bbox'][3] for region in text_regions]
        
        x1, y1 = min(all_x1), min(all_y1)
        x2, y2 = max(all_x2), max(all_y2)
        
        # 计算平均置信度
        avg_confidence = sum([region['confidence'] for region in text_regions]) / len(text_regions)
        
        merged_region = {
            'text': merged_text,
            'confidence': avg_confidence,
            'bbox': [x1, y1, x2, y2],
            'center': [(x1 + x2) / 2, (y1 + y2) / 2],
            'width': x2 - x1,
            'height': y2 - y1,
            'area': (x2 - x1) * (y2 - y1),
            'merged_count': len(text_regions)
        }
        
        return merged_region
    
    def sort_text_by_position(self, ocr_results: List[Dict[str, Any]], 
                             sort_order: str = "top_to_bottom") -> List[Dict[str, Any]]:
        """
        按位置排序文本
        
        Args:
            ocr_results: OCR识别结果
            sort_order: 排序方式
            
        Returns:
            排序后的OCR结果
        """
        if sort_order == "top_to_bottom":
            # 按Y坐标排序（从上到下）
            sorted_results = sorted(ocr_results, key=lambda x: x['center'][1])
        elif sort_order == "left_to_right":
            # 按X坐标排序（从左到右）
            sorted_results = sorted(ocr_results, key=lambda x: x['center'][0])
        else:
            # 综合排序：先按Y坐标，再按X坐标
            sorted_results = sorted(ocr_results, key=lambda x: (x['center'][1], x['center'][0]))
        
        return sorted_results
    
    def extract_text_content(self, ocr_results: List[Dict[str, Any]]) -> str:
        """
        提取纯文本内容
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            纯文本内容
        """
        # 按位置排序
        sorted_results = self.sort_text_by_position(ocr_results)
        
        # 提取文本
        text_lines = [result['text'] for result in sorted_results]
        
        return '\n'.join(text_lines)
    
    def get_text_statistics(self, ocr_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取文本统计信息
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            统计信息
        """
        if not ocr_results:
            return {}
        
        confidences = [result['confidence'] for result in ocr_results]
        areas = [result['area'] for result in ocr_results]
        text_lengths = [len(result['text']) for result in ocr_results]
        
        statistics = {
            'total_text_regions': len(ocr_results),
            'average_confidence': sum(confidences) / len(confidences),
            'min_confidence': min(confidences),
            'max_confidence': max(confidences),
            'total_text_area': sum(areas),
            'average_text_area': sum(areas) / len(areas),
            'total_characters': sum(text_lengths),
            'average_text_length': sum(text_lengths) / len(text_lengths)
        }
        
        return statistics
