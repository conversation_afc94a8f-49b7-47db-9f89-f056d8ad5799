"""
知识库构建器 - RAG知识库构建和管理
"""
import pickle
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import logging

try:
    from langchain.vectorstores import FAISS
    from langchain.schema import Document
    from langchain_community.retrievers import BM25Retriever
    from langchain_huggingface import HuggingFaceEmbeddings
except ImportError:
    logging.warning("LangChain packages not installed. Please install them for knowledge base functionality.")
    FAISS = None
    Document = None
    BM25Retriever = None
    HuggingFaceEmbeddings = None

try:
    from ..config.settings import settings
    from ..utils.text_processor import TextProcessor
except ImportError:
    from config.settings import settings
    from utils.text_processor import TextProcessor

logger = logging.getLogger(__name__)

class KnowledgeBuilder:
    """知识库构建器"""
    
    def __init__(self, embedding_model_name: str = None, knowledge_base_path: str = None):
        """
        初始化知识库构建器
        
        Args:
            embedding_model_name: 嵌入模型名称
            knowledge_base_path: 知识库保存路径
        """
        self.embedding_model_name = embedding_model_name or settings.EMBEDDING_MODEL_NAME
        self.knowledge_base_path = Path(knowledge_base_path) if knowledge_base_path else settings.OUTPUT_DIR / "knowledge_base"
        self.knowledge_base_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.text_processor = TextProcessor()
        self.embeddings = None
        self.vector_store = None
        self.bm25_retriever = None
        self.documents = []
        
        # 初始化嵌入模型
        self._initialize_embeddings()
        
        logger.info(f"Knowledge builder initialized with model: {self.embedding_model_name}")
    
    def _initialize_embeddings(self):
        """初始化嵌入模型"""
        try:
            if HuggingFaceEmbeddings is not None:
                self.embeddings = HuggingFaceEmbeddings(
                    model_name=self.embedding_model_name,
                    model_kwargs={'device': 'cuda' if settings.USE_GPU else 'cpu'},
                    encode_kwargs={'normalize_embeddings': True}
                )
                logger.info("Embedding model loaded successfully")
            else:
                raise ImportError("HuggingFaceEmbeddings not available")
                
        except Exception as e:
            logger.error(f"Embedding model initialization failed: {e}")
            raise
    
    def add_documents(self, documents: List[Union[Document, Dict[str, Any]]]) -> bool:
        """
        添加文档到知识库
        
        Args:
            documents: 文档列表
            
        Returns:
            是否成功
        """
        try:
            if not documents:
                logger.warning("No documents to add")
                return False
            
            # 转换文档格式
            processed_docs = []
            for doc in documents:
                if isinstance(doc, dict):
                    if Document is not None:
                        processed_doc = Document(
                            page_content=doc.get('page_content', ''),
                            metadata=doc.get('metadata', {})
                        )
                    else:
                        processed_doc = doc
                else:
                    processed_doc = doc
                
                processed_docs.append(processed_doc)
            
            # 添加到文档列表
            self.documents.extend(processed_docs)
            
            logger.info(f"Added {len(processed_docs)} documents to knowledge base")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return False
    
    def build_vector_store(self) -> bool:
        """
        构建向量存储
        
        Returns:
            是否成功
        """
        try:
            if not self.documents:
                logger.warning("No documents available for vector store")
                return False
            
            if FAISS is None or self.embeddings is None:
                logger.error("FAISS or embeddings not available")
                return False
            
            # 构建FAISS向量存储
            self.vector_store = FAISS.from_documents(
                documents=self.documents,
                embedding=self.embeddings
            )
            
            logger.info(f"Vector store built with {len(self.documents)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Vector store building failed: {e}")
            return False
    
    def build_bm25_index(self) -> bool:
        """
        构建BM25索引
        
        Returns:
            是否成功
        """
        try:
            if not self.documents:
                logger.warning("No documents available for BM25 index")
                return False
            
            if BM25Retriever is None:
                logger.error("BM25Retriever not available")
                return False
            
            # 预处理文档用于BM25
            processed_docs = []
            for doc in self.documents:
                if hasattr(doc, 'page_content'):
                    content = doc.page_content
                else:
                    content = doc.get('page_content', '')
                
                # 使用jieba分词
                tokens = self.text_processor.preprocess_for_bm25(content)
                
                # 创建新文档对象，内容为分词结果
                if Document is not None:
                    processed_doc = Document(
                        page_content=' '.join(tokens),
                        metadata=doc.metadata if hasattr(doc, 'metadata') else doc.get('metadata', {})
                    )
                else:
                    processed_doc = {
                        'page_content': ' '.join(tokens),
                        'metadata': doc.get('metadata', {}) if isinstance(doc, dict) else {}
                    }
                
                processed_docs.append(processed_doc)
            
            # 构建BM25检索器
            self.bm25_retriever = BM25Retriever.from_documents(
                documents=processed_docs,
                k=settings.TOP_K
            )
            
            # 设置BM25参数
            if hasattr(self.bm25_retriever, 'k1'):
                self.bm25_retriever.k1 = settings.BM25_K1
            if hasattr(self.bm25_retriever, 'b'):
                self.bm25_retriever.b = settings.BM25_B
            
            logger.info(f"BM25 index built with {len(processed_docs)} documents")
            return True

        except Exception as e:
            logger.error(f"BM25 index building failed: {e}")
            return False

    def build_knowledge_base(self) -> bool:
        """
        构建完整的知识库（向量存储 + BM25索引）

        Returns:
            是否成功
        """
        try:
            logger.info("Building complete knowledge base...")

            # 构建向量存储
            vector_success = self.build_vector_store()
            if not vector_success:
                logger.error("Vector store building failed")
                return False

            # 构建BM25索引
            bm25_success = self.build_bm25_index()
            if not bm25_success:
                logger.error("BM25 index building failed")
                return False

            logger.info("Knowledge base built successfully")
            return True

        except Exception as e:
            logger.error(f"Knowledge base building failed: {e}")
            return False

    def save_knowledge_base(self, save_path: str = None) -> bool:
        """
        保存知识库

        Args:
            save_path: 保存路径

        Returns:
            是否成功
        """
        try:
            save_dir = Path(save_path) if save_path else self.knowledge_base_path
            save_dir.mkdir(parents=True, exist_ok=True)

            # 保存向量存储
            if self.vector_store is not None:
                vector_path = save_dir / "vector_store"
                self.vector_store.save_local(str(vector_path))
                logger.info(f"Vector store saved to {vector_path}")

            # 保存BM25检索器
            if self.bm25_retriever is not None:
                bm25_path = save_dir / "bm25_retriever.pkl"
                with open(bm25_path, 'wb') as f:
                    pickle.dump(self.bm25_retriever, f)
                logger.info(f"BM25 retriever saved to {bm25_path}")

            # 保存文档
            if self.documents:
                docs_path = save_dir / "documents.pkl"
                with open(docs_path, 'wb') as f:
                    pickle.dump(self.documents, f)
                logger.info(f"Documents saved to {docs_path}")

            # 保存元数据
            metadata = {
                'embedding_model': self.embedding_model_name,
                'document_count': len(self.documents),
                'has_vector_store': self.vector_store is not None,
                'has_bm25_index': self.bm25_retriever is not None
            }

            metadata_path = save_dir / "metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            logger.info(f"Knowledge base saved to {save_dir}")
            return True

        except Exception as e:
            logger.error(f"Knowledge base saving failed: {e}")
            return False

    def load_knowledge_base(self, load_path: str = None) -> bool:
        """
        加载知识库

        Args:
            load_path: 加载路径

        Returns:
            是否成功
        """
        try:
            load_dir = Path(load_path) if load_path else self.knowledge_base_path

            if not load_dir.exists():
                logger.warning(f"Knowledge base path not found: {load_dir}")
                return False

            # 加载元数据
            metadata_path = load_dir / "metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                logger.info(f"Loaded metadata: {metadata}")

            # 加载向量存储
            vector_path = load_dir / "vector_store"
            if vector_path.exists() and FAISS is not None:
                self.vector_store = FAISS.load_local(
                    str(vector_path),
                    self.embeddings,
                    allow_dangerous_deserialization=True
                )
                logger.info("Vector store loaded successfully")

            # 加载BM25检索器
            bm25_path = load_dir / "bm25_retriever.pkl"
            if bm25_path.exists():
                with open(bm25_path, 'rb') as f:
                    self.bm25_retriever = pickle.load(f)
                logger.info("BM25 retriever loaded successfully")

            # 加载文档
            docs_path = load_dir / "documents.pkl"
            if docs_path.exists():
                with open(docs_path, 'rb') as f:
                    self.documents = pickle.load(f)
                logger.info(f"Loaded {len(self.documents)} documents")

            logger.info(f"Knowledge base loaded from {load_dir}")
            return True

        except Exception as e:
            logger.error(f"Knowledge base loading failed: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息

        Returns:
            统计信息
        """
        stats = {
            'document_count': len(self.documents),
            'has_vector_store': self.vector_store is not None,
            'has_bm25_index': self.bm25_retriever is not None,
            'embedding_model': self.embedding_model_name
        }

        if self.documents:
            # 计算文档统计
            total_chars = 0
            total_words = 0

            for doc in self.documents:
                content = doc.page_content if hasattr(doc, 'page_content') else doc.get('page_content', '')
                total_chars += len(content)
                total_words += len(content.split())

            stats.update({
                'total_characters': total_chars,
                'total_words': total_words,
                'avg_chars_per_doc': total_chars / len(self.documents),
                'avg_words_per_doc': total_words / len(self.documents)
            })

        return stats

    def clear_knowledge_base(self):
        """清空知识库"""
        self.documents = []
        self.vector_store = None
        self.bm25_retriever = None
        logger.info("Knowledge base cleared")
