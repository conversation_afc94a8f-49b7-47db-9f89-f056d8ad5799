"""
工业智维助手主程序
"""
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

try:
    from .config.settings import settings
except ImportError:
    from config.settings import settings
try:
    from .core.pdf_parser import PDFParser
    from .core.table_extractor import TableExtractor
    from .core.multimodal_processor import MultimodalProcessor
    from .core.knowledge_builder import KnowledgeBuilder
    from .core.retrieval_generator import RetrievalGenerator
    from .utils.layout_analyzer import LayoutAnalyzer
    from .utils.text_processor import TextProcessor
except ImportError:
    from core.pdf_parser import PDFParser
    from core.table_extractor import TableExtractor
    from core.multimodal_processor import MultimodalProcessor
    from core.knowledge_builder import KnowledgeBuilder
    from core.retrieval_generator import RetrievalGenerator
    from utils.layout_analyzer import LayoutAnalyzer
    from utils.text_processor import TextProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IndustrialRAGAssistant:
    """工业智维助手主类"""
    
    def __init__(self):
        """初始化助手"""
        self.pdf_parser = None
        self.table_extractor = None
        self.multimodal_processor = None
        self.knowledge_builder = None
        self.retrieval_generator = None
        
        logger.info("Industrial RAG Assistant initialized")
    
    def initialize_components(self):
        """初始化所有组件"""
        try:
            logger.info("Initializing components...")
            
            # 初始化PDF解析器
            self.pdf_parser = PDFParser()
            logger.info("PDF parser initialized")
            
            # 初始化表格提取器
            self.table_extractor = TableExtractor()
            logger.info("Table extractor initialized")
            
            # 初始化多模态处理器
            self.multimodal_processor = MultimodalProcessor()
            logger.info("Multimodal processor initialized")
            
            # 初始化知识库构建器
            self.knowledge_builder = KnowledgeBuilder()
            logger.info("Knowledge builder initialized")
            
            # 初始化检索生成器
            self.retrieval_generator = RetrievalGenerator()
            logger.info("Retrieval generator initialized")
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Component initialization failed: {e}")
            raise
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Dict[str, Any]:
        """
        处理PDF文档
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录
            
        Returns:
            处理结果
        """
        try:
            logger.info(f"Processing PDF: {pdf_path}")
            
            if not self.pdf_parser:
                raise RuntimeError("PDF parser not initialized")
            
            # 解析PDF
            parse_result = self.pdf_parser.parse_pdf(pdf_path, output_dir)
            
            if not parse_result['success']:
                return parse_result
            
            # 处理每个页面的内容
            processed_pages = []
            
            for page_info in parse_result['pages']:
                page_result = {
                    'page_number': page_info['page_number'],
                    'regions': []
                }
                
                # 处理每个区域
                for region in page_info['regions']:
                    region_result = self._process_region(region)
                    page_result['regions'].append(region_result)
                
                processed_pages.append(page_result)
            
            result = {
                'success': True,
                'pdf_path': pdf_path,
                'pages': processed_pages,
                'total_pages': len(processed_pages)
            }
            
            logger.info(f"PDF processing completed: {len(processed_pages)} pages")
            return result
            
        except Exception as e:
            logger.error(f"PDF processing failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_region(self, region: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个区域
        
        Args:
            region: 区域信息
            
        Returns:
            处理结果
        """
        try:
            region_type = region['type']
            region_image = region['image']
            
            result = {
                'type': region_type,
                'bbox': region['bbox'],
                'content': '',
                'markdown': ''
            }
            
            if region_type == 'table':
                # 表格提取
                if self.table_extractor:
                    table_result = self.table_extractor.extract_table(region_image)
                    result['content'] = table_result.get('structured_table', [])
                    result['markdown'] = table_result.get('markdown', '')
            
            elif region_type in ['text', 'figure']:
                # 多模态理解
                if self.multimodal_processor:
                    content_type = 'text' if region_type == 'text' else 'figure'
                    markdown_content = self.multimodal_processor.process_image_to_markdown(
                        region_image, content_type
                    )
                    result['content'] = markdown_content
                    result['markdown'] = markdown_content
            
            return result
            
        except Exception as e:
            logger.error(f"Region processing failed: {e}")
            return {
                'type': region.get('type', 'unknown'),
                'bbox': region.get('bbox', []),
                'content': '',
                'markdown': '',
                'error': str(e)
            }
    
    def build_knowledge_base(self, processed_results: List[Dict[str, Any]]) -> bool:
        """
        构建知识库
        
        Args:
            processed_results: 处理结果列表
            
        Returns:
            是否成功
        """
        try:
            logger.info("Building knowledge base...")
            
            if not self.knowledge_builder:
                raise RuntimeError("Knowledge builder not initialized")
            
            # 收集所有文档
            all_documents = []
            
            for result in processed_results:
                if not result.get('success', False):
                    continue
                
                for page in result.get('pages', []):
                    for region in page.get('regions', []):
                        markdown_content = region.get('markdown', '')
                        if markdown_content.strip():
                            # 创建文档元数据
                            metadata = {
                                'source': result.get('pdf_path', ''),
                                'page_number': page.get('page_number', 0),
                                'region_type': region.get('type', ''),
                                'bbox': region.get('bbox', [])
                            }
                            
                            # 分割文档
                            text_processor = TextProcessor()
                            if region.get('type') == 'text':
                                docs = text_processor.split_markdown_by_sections(
                                    markdown_content, metadata
                                )
                            else:
                                docs = text_processor.split_text(
                                    markdown_content, metadata
                                )
                            
                            all_documents.extend(docs)
            
            if not all_documents:
                logger.warning("No documents to add to knowledge base")
                return False
            
            # 添加文档到知识库
            success = self.knowledge_builder.add_documents(all_documents)
            if not success:
                return False
            
            # 构建知识库
            success = self.knowledge_builder.build_knowledge_base()
            if not success:
                return False
            
            # 保存知识库
            success = self.knowledge_builder.save_knowledge_base()
            
            logger.info(f"Knowledge base built with {len(all_documents)} documents")
            return success
            
        except Exception as e:
            logger.error(f"Knowledge base building failed: {e}")
            return False
    
    def query(self, question: str) -> str:
        """
        查询问答
        
        Args:
            question: 用户问题
            
        Returns:
            答案
        """
        try:
            logger.info(f"Processing query: {question}")
            
            if not self.retrieval_generator or not self.knowledge_builder:
                raise RuntimeError("Components not initialized")
            
            # 检索相关文档
            retrieved_docs = self.retrieval_generator.hybrid_retrieve(
                question,
                self.knowledge_builder.vector_store,
                self.knowledge_builder.bm25_retriever
            )
            
            if not retrieved_docs:
                return "抱歉，我没有找到相关信息。"
            
            # 生成答案
            answer = self.retrieval_generator.generate_answer(
                question, retrieved_docs
            )
            
            logger.info("Query processed successfully")
            return answer
            
        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            return f"查询处理失败：{str(e)}"
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.multimodal_processor:
                self.multimodal_processor.cleanup_resources()
            
            if self.retrieval_generator:
                self.retrieval_generator.cleanup_resources()
            
            logger.info("Resources cleaned up")
            
        except Exception as e:
            logger.warning(f"Resource cleanup failed: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="工业智维助手")
    parser.add_argument("--pdf", type=str, help="PDF文件路径")
    parser.add_argument("--output", type=str, help="输出目录")
    parser.add_argument("--query", type=str, help="查询问题")
    parser.add_argument("--build-kb", action="store_true", help="构建知识库")
    
    args = parser.parse_args()
    
    # 创建助手实例
    assistant = IndustrialRAGAssistant()
    
    try:
        # 初始化组件
        assistant.initialize_components()
        
        if args.pdf:
            # 处理PDF
            result = assistant.process_pdf(args.pdf, args.output)
            print(f"PDF处理结果: {result['success']}")
            
            if args.build_kb and result['success']:
                # 构建知识库
                kb_success = assistant.build_knowledge_base([result])
                print(f"知识库构建结果: {kb_success}")
        
        elif args.query:
            # 加载知识库
            kb_loaded = assistant.knowledge_builder.load_knowledge_base()
            if not kb_loaded:
                print("知识库加载失败，请先构建知识库")
                return
            
            # 查询
            answer = assistant.query(args.query)
            print(f"答案: {answer}")
        
        else:
            print("请指定操作：--pdf 处理PDF文档，--query 查询问题")
    
    finally:
        # 清理资源
        assistant.cleanup()

if __name__ == "__main__":
    main()
