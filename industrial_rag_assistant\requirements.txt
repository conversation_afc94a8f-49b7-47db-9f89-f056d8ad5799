# 核心依赖
torch>=2.0.0
transformers>=4.35.0
vllm>=0.2.0
qwen_vl_utils

# PDF处理
PyMuPDF>=1.23.0
pdf2image>=3.1.0
pillow>=10.0.0

# 布局分析和OCR
rapid_layout
paddleocr>=2.7.0
opencv-python>=4.8.0

# 表格识别
modelscope>=1.9.0

# RAG相关
langchain>=0.1.0
langchain-community>=0.0.10
langchain-huggingface>=0.0.1
faiss-cpu>=1.7.4
sentence-transformers>=2.2.2

# 文本处理
jieba>=0.42.1
numpy>=1.24.0
pandas>=2.0.0

# 几何计算
shapely>=2.0.0

# 工具库
tqdm>=4.65.0
loguru>=0.7.0
pydantic>=2.0.0
python-dotenv>=1.0.0

# 可选GPU支持
# faiss-gpu>=1.7.4  # 如果有GPU，可以替换faiss-cpu

# 开发和测试
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0

# Web框架（可选）
fastapi>=0.104.0
uvicorn>=0.24.0
streamlit>=1.28.0

# 其他依赖
requests>=2.31.0
python-multipart>=0.0.6
aiofiles>=23.2.1
accelerate>=0.24.0
pydantic-settings>=2.0.0
click>=8.1.0
