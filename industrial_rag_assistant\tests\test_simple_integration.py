"""
简化集成测试 - 不依赖复杂模块
"""
import pytest
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestSimpleIntegration:
    """简化集成测试类"""
    
    def test_text_processing_workflow(self):
        """测试文本处理工作流"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试完整的文本处理流程
        text = """
        # 工业设备维护手册
        
        ## 第一章 概述
        本手册介绍了工业设备的维护方法和注意事项。
        
        ### 1.1 设备分类
        - 机械设备
        - 电气设备
        - 控制系统
        
        ## 第二章 维护流程
        设备维护包括以下步骤：
        1. 设备检查
        2. 故障诊断
        3. 维修处理
        4. 测试验证
        """
        
        metadata = {"source": "maintenance_manual.md", "type": "manual"}
        
        # 测试Markdown分割
        docs = processor.split_markdown_by_sections(text, metadata)

        assert len(docs) >= 1  # 至少有一个文档
        
        # 检查文档内容
        content_text = ' '.join([doc.page_content for doc in docs])
        assert "工业设备维护" in content_text
        assert "维护流程" in content_text
        
        # 测试BM25预处理
        for doc in docs:
            processed = processor.preprocess_for_bm25(doc.page_content)
            assert isinstance(processed, list)
            if processed:  # 如果有内容
                assert all(isinstance(token, str) for token in processed)
    
    def test_configuration_integration(self):
        """测试配置集成"""
        from config.settings import settings
        
        # 测试配置的一致性
        assert settings.CHUNK_SIZE > 0
        assert settings.CHUNK_OVERLAP < settings.CHUNK_SIZE
        assert settings.TOP_K > 0
        assert 0 <= settings.TEMPERATURE <= 2.0
        assert 0 <= settings.TOP_P <= 1.0
        
        # 测试路径配置
        assert settings.PROJECT_ROOT.exists()
        assert settings.DATA_DIR.name == "data"
        assert settings.MODELS_DIR.name == "models"
        
        # 测试提示词模板
        assert "{context}" in settings.USER_PROMPT_TEMPLATE
        assert "{question}" in settings.USER_PROMPT_TEMPLATE
    
    def test_error_handling(self):
        """测试错误处理"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试空文本处理
        empty_docs = processor.split_text("", {"source": "empty"})
        assert isinstance(empty_docs, list)
        
        # 测试无效输入
        invalid_processed = processor.preprocess_for_bm25(None)
        assert invalid_processed == []
        
        # 测试特殊字符
        special_text = "!@#$%^&*()"
        special_processed = processor.preprocess_for_bm25(special_text)
        assert isinstance(special_processed, list)
    
    def test_chinese_text_processing(self):
        """测试中文文本处理"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 测试中文分词
        chinese_text = "工业设备维护是一项重要的技术工作"
        processed = processor.preprocess_for_bm25(chinese_text)
        
        assert isinstance(processed, list)
        assert len(processed) > 0
        assert "工业" in processed
        assert "设备" in processed
        assert "维护" in processed
        
        # 测试混合中英文
        mixed_text = "工业设备maintenance和repair技术"
        mixed_processed = processor.preprocess_for_bm25(mixed_text)
        
        assert "工业" in mixed_processed
        assert "设备" in mixed_processed
        assert "maintenance" in mixed_processed
        assert "repair" in mixed_processed
        assert "技术" in mixed_processed
    
    def test_document_chunking(self):
        """测试文档分块"""
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 创建长文本
        long_text = "这是一个测试段落。" * 100  # 创建足够长的文本
        metadata = {"source": "test_long.txt", "page": 1}
        
        docs = processor.split_text(long_text, metadata)
        
        assert isinstance(docs, list)
        assert len(docs) > 1  # 应该被分成多个块
        
        # 检查每个文档块
        for doc in docs:
            assert hasattr(doc, 'page_content')
            assert hasattr(doc, 'metadata')
            assert doc.metadata['source'] == 'test_long.txt'
            assert doc.metadata['page'] == 1
            assert len(doc.page_content) > 0

class TestSystemRequirements:
    """系统需求测试类"""
    
    def test_python_version(self):
        """测试Python版本"""
        import sys
        version = sys.version_info
        assert version.major == 3
        assert version.minor >= 8  # 要求Python 3.8+
    
    def test_required_packages(self):
        """测试必需的包"""
        required_packages = [
            'jieba',
            'cv2',
            'pydantic',
            'langchain',
            'pytest'
        ]
        
        for package in required_packages:
            try:
                if package == 'cv2':
                    import cv2
                else:
                    __import__(package)
            except ImportError:
                pytest.fail(f"Required package {package} not installed")
    
    def test_directory_structure(self):
        """测试目录结构"""
        project_root = Path(__file__).parent.parent
        
        required_dirs = [
            "config",
            "core",
            "utils",
            "tests",
            "data",
            "models"
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            assert dir_path.exists(), f"Required directory {dir_name} not found"
    
    def test_configuration_files(self):
        """测试配置文件"""
        project_root = Path(__file__).parent.parent
        
        required_files = [
            "config/settings.py",
            "main.py",
            "requirements.txt",
            "setup.py",
            "README.md"
        ]
        
        for file_path in required_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"Required file {file_path} not found"
            assert full_path.stat().st_size > 0, f"File {file_path} is empty"

class TestPerformance:
    """性能测试类"""
    
    def test_text_processing_performance(self):
        """测试文本处理性能"""
        import time
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 创建大量文本
        large_text = "这是一个性能测试文档。包含大量的中文内容用于测试分词和处理性能。" * 1000
        
        start_time = time.time()
        processed = processor.preprocess_for_bm25(large_text)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        assert isinstance(processed, list)
        assert len(processed) > 0
        assert processing_time < 10.0  # 应该在10秒内完成
        
        print(f"处理 {len(large_text)} 字符耗时: {processing_time:.2f} 秒")
        print(f"生成 {len(processed)} 个词汇")
    
    def test_document_splitting_performance(self):
        """测试文档分割性能"""
        import time
        from utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 创建大文档
        large_doc = "# 第一章\n这是第一章的内容。\n\n" * 500
        metadata = {"source": "large_doc.md"}
        
        start_time = time.time()
        docs = processor.split_markdown_by_sections(large_doc, metadata)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        assert isinstance(docs, list)
        assert len(docs) > 0
        assert processing_time < 5.0  # 应该在5秒内完成
        
        print(f"分割 {len(large_doc)} 字符文档耗时: {processing_time:.2f} 秒")
        print(f"生成 {len(docs)} 个文档块")

if __name__ == "__main__":
    # 运行简化集成测试
    pytest.main([__file__, "-v", "--tb=short"])
