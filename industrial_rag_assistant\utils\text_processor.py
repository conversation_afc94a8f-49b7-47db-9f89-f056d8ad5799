"""
文本处理器 - 文本分块、清理和预处理
"""
import re
import jieba
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.schema import Document
except ImportError:
    logging.warning("<PERSON><PERSON><PERSON><PERSON> not installed. Please install it for text processing.")
    RecursiveCharacterTextSplitter = None
    Document = None

try:
    from ..config.settings import settings
except ImportError:
    from config.settings import settings

logger = logging.getLogger(__name__)

class TextProcessor:
    """文本处理器"""
    
    def __init__(self, chunk_size: int = None, chunk_overlap: int = None):
        """
        初始化文本处理器
        
        Args:
            chunk_size: 文本块大小
            chunk_overlap: 文本块重叠大小
        """
        self.chunk_size = chunk_size or settings.CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or settings.CHUNK_OVERLAP
        
        # 初始化文本分割器
        if RecursiveCharacterTextSplitter is not None:
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                length_function=len,
                separators=['\n\n', '\n', '。', '！', '？', '；', '，', ' ', '']
            )
        else:
            self.text_splitter = None
            logger.warning("RecursiveCharacterTextSplitter not available")
        
        # 初始化jieba分词
        jieba.initialize()
        
        logger.info(f"Text processor initialized with chunk_size: {self.chunk_size}, chunk_overlap: {self.chunk_overlap}")
    
    def clean_text(self, text: str) -> str:
        """
        清理文本
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留中文、英文、数字、常用标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,;:!?()[\]{}""''—-]', '', text)
        
        # 移除多余的换行符
        text = re.sub(r'\n+', '\n', text)
        
        # 移除首尾空白
        text = text.strip()
        
        return text
    
    def split_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        分割文本为文档块
        
        Args:
            text: 输入文本
            metadata: 元数据
            
        Returns:
            文档块列表
        """
        if not text or not self.text_splitter:
            return []
        
        try:
            # 清理文本
            cleaned_text = self.clean_text(text)
            
            if not cleaned_text:
                return []
            
            # 分割文本
            chunks = self.text_splitter.split_text(cleaned_text)
            
            # 创建文档对象
            documents = []
            for i, chunk in enumerate(chunks):
                doc_metadata = metadata.copy() if metadata else {}
                doc_metadata.update({
                    'chunk_id': i,
                    'chunk_size': len(chunk),
                    'total_chunks': len(chunks)
                })
                
                if Document is not None:
                    doc = Document(page_content=chunk, metadata=doc_metadata)
                else:
                    doc = {
                        'page_content': chunk,
                        'metadata': doc_metadata
                    }
                
                documents.append(doc)
            
            logger.info(f"Text split into {len(documents)} chunks")
            return documents
            
        except Exception as e:
            logger.error(f"Text splitting failed: {e}")
            return []
    
    def split_markdown_by_sections(self, markdown_text: str, 
                                  metadata: Dict[str, Any] = None) -> List[Document]:
        """
        按章节分割Markdown文本
        
        Args:
            markdown_text: Markdown文本
            metadata: 元数据
            
        Returns:
            文档块列表
        """
        if not markdown_text:
            return []
        
        try:
            # 按标题分割
            sections = re.split(r'\n(?=#{1,6}\s)', markdown_text)
            
            documents = []
            for i, section in enumerate(sections):
                if not section.strip():
                    continue
                
                # 提取标题
                title_match = re.match(r'^(#{1,6})\s+(.+)', section)
                if title_match:
                    level = len(title_match.group(1))
                    title = title_match.group(2).strip()
                else:
                    level = 0
                    title = f"Section {i+1}"
                
                # 如果章节太长，进一步分割
                if len(section) > self.chunk_size:
                    sub_chunks = self.split_text(section, metadata)
                    for j, sub_chunk in enumerate(sub_chunks):
                        sub_metadata = sub_chunk.metadata if hasattr(sub_chunk, 'metadata') else sub_chunk.get('metadata', {})
                        sub_metadata.update({
                            'section_title': title,
                            'section_level': level,
                            'section_id': i,
                            'sub_chunk_id': j
                        })
                        documents.append(sub_chunk)
                else:
                    # 创建文档
                    doc_metadata = metadata.copy() if metadata else {}
                    doc_metadata.update({
                        'section_title': title,
                        'section_level': level,
                        'section_id': i,
                        'chunk_size': len(section)
                    })
                    
                    if Document is not None:
                        doc = Document(page_content=section, metadata=doc_metadata)
                    else:
                        doc = {
                            'page_content': section,
                            'metadata': doc_metadata
                        }
                    
                    documents.append(doc)
            
            logger.info(f"Markdown split into {len(documents)} sections")
            return documents
            
        except Exception as e:
            logger.error(f"Markdown splitting failed: {e}")
            return self.split_text(markdown_text, metadata)
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            top_k: 返回关键词数量
            
        Returns:
            关键词列表
        """
        if not text:
            return []
        
        try:
            # 使用jieba分词
            words = jieba.cut(text)
            
            # 过滤停用词和短词
            filtered_words = []
            for word in words:
                word = word.strip()
                if len(word) > 1 and not self._is_stopword(word):
                    filtered_words.append(word)
            
            # 统计词频
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # 按频率排序
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            
            # 返回top_k关键词
            keywords = [word for word, freq in sorted_words[:top_k]]
            
            return keywords
            
        except Exception as e:
            logger.error(f"Keyword extraction failed: {e}")
            return []
    
    def _is_stopword(self, word: str) -> bool:
        """
        判断是否为停用词
        
        Args:
            word: 词语
            
        Returns:
            是否为停用词
        """
        # 简单的停用词列表
        stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么',
            '为什么', '哪里', '哪个', '如何', '因为', '所以', '但是', '然后', '或者',
            '以及', '以上', '以下', '之前', '之后', '之间', '当中', '其中', '包括',
            '根据', '按照', '通过', '由于', '关于', '对于', '针对', '基于'
        }
        
        return word in stopwords or len(word) < 2
    
    def preprocess_for_bm25(self, text: str) -> List[str]:
        """
        为BM25预处理文本
        
        Args:
            text: 输入文本
            
        Returns:
            分词结果
        """
        if not text:
            return []
        
        try:
            # 清理文本
            cleaned_text = self.clean_text(text)
            
            # 分词
            words = jieba.cut(cleaned_text)
            
            # 过滤和清理
            processed_words = []
            for word in words:
                word = word.strip()
                if len(word) > 1 and not self._is_stopword(word):
                    processed_words.append(word)
            
            return processed_words
            
        except Exception as e:
            logger.error(f"BM25 preprocessing failed: {e}")
            return []
    
    def merge_documents(self, documents: List[Document]) -> str:
        """
        合并文档内容
        
        Args:
            documents: 文档列表
            
        Returns:
            合并后的文本
        """
        if not documents:
            return ""
        
        contents = []
        for doc in documents:
            if hasattr(doc, 'page_content'):
                contents.append(doc.page_content)
            elif isinstance(doc, dict) and 'page_content' in doc:
                contents.append(doc['page_content'])
            else:
                contents.append(str(doc))
        
        return '\n\n'.join(contents)
    
    def get_text_statistics(self, text: str) -> Dict[str, Any]:
        """
        获取文本统计信息
        
        Args:
            text: 输入文本
            
        Returns:
            统计信息
        """
        if not text:
            return {}
        
        try:
            # 基本统计
            char_count = len(text)
            word_count = len(text.split())
            line_count = len(text.split('\n'))
            
            # 中文字符统计
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            
            # 英文单词统计
            english_words = len(re.findall(r'[a-zA-Z]+', text))
            
            # 数字统计
            numbers = len(re.findall(r'\d+', text))
            
            # 标点符号统计
            punctuation = len(re.findall(r'[.,;:!?()[\]{}""''—-]', text))
            
            statistics = {
                'char_count': char_count,
                'word_count': word_count,
                'line_count': line_count,
                'chinese_chars': chinese_chars,
                'english_words': english_words,
                'numbers': numbers,
                'punctuation': punctuation,
                'avg_words_per_line': word_count / line_count if line_count > 0 else 0
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"Text statistics calculation failed: {e}")
            return {}
