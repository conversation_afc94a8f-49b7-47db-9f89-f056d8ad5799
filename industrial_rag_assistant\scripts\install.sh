#!/bin/bash

# 工业智维助手安装脚本
# Industrial RAG Assistant Installation Script

set -e

echo "🚀 开始安装工业智维助手..."
echo "Starting Industrial RAG Assistant installation..."

# 检查Python版本
echo "📋 检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
    echo "✅ Python版本检查通过: $python_version"
else
    echo "❌ Python版本过低，需要3.8+，当前版本: $python_version"
    exit 1
fi

# 检查CUDA
echo "🔍 检查CUDA环境..."
if command -v nvidia-smi &> /dev/null; then
    echo "✅ 检测到NVIDIA GPU"
    nvidia-smi --query-gpu=name --format=csv,noheader
else
    echo "⚠️  未检测到NVIDIA GPU，将使用CPU模式"
fi

# 创建虚拟环境
echo "🏗️  创建虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ 虚拟环境创建成功"
else
    echo "📁 虚拟环境已存在"
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

# 安装PyTorch (根据CUDA版本)
echo "🔥 安装PyTorch..."
if command -v nvidia-smi &> /dev/null; then
    # 检查CUDA版本
    cuda_version=$(nvidia-smi | grep "CUDA Version" | awk '{print $9}' | cut -d. -f1,2)
    echo "检测到CUDA版本: $cuda_version"
    
    if [ "$cuda_version" = "11.8" ] || [ "$cuda_version" = "12.1" ]; then
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    else
        pip install torch torchvision torchaudio
    fi
else
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
fi

# 安装项目依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

# 创建必要目录
echo "📁 创建项目目录..."
mkdir -p data models output logs

# 下载模型提示
echo "📥 模型下载提示..."
echo "请手动下载以下模型到models目录："
echo "1. Qwen2.5-VL-7B-Instruct"
echo "2. Qwen2.5-7B-Instruct"
echo "3. bge-large-zh-v1.5"
echo ""
echo "下载命令示例："
echo "huggingface-cli download Qwen/Qwen2.5-VL-7B-Instruct --local-dir ./models/Qwen2.5-VL-7B-Instruct"
echo "huggingface-cli download Qwen/Qwen2.5-7B-Instruct --local-dir ./models/Qwen2.5-7B-Instruct"
echo "huggingface-cli download BAAI/bge-large-zh-v1.5 --local-dir ./models/bge-large-zh-v1.5"

# 配置文件检查
echo "⚙️  检查配置文件..."
if [ ! -f "config/settings.py" ]; then
    echo "❌ 配置文件不存在，请检查项目结构"
    exit 1
else
    echo "✅ 配置文件存在"
fi

# 运行测试
echo "🧪 运行基础测试..."
python -c "
import torch
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA设备数量: {torch.cuda.device_count()}')
    print(f'当前CUDA设备: {torch.cuda.current_device()}')
"

echo ""
echo "🎉 安装完成！"
echo "Installation completed!"
echo ""
echo "下一步："
echo "1. 下载所需模型到models目录"
echo "2. 编辑config/settings.py配置模型路径"
echo "3. 运行: python main.py --help 查看使用方法"
echo ""
echo "Next steps:"
echo "1. Download required models to models directory"
echo "2. Edit config/settings.py to configure model paths"
echo "3. Run: python main.py --help to see usage"
