"""
此模块实现了将 PDF 文件转换为 Markdown 文件的功能。
借助布局检测和多模态大模型，提取 PDF 中的文本和图片信息，并转换为 Markdown 格式。
"""
# 导入操作系统相关功能模块，用于文件和目录操作
import os
# 导入正则表达式模块，用于字符串匹配和处理
import re
# 从 typing 模块导入多种类型注解，增强代码的可读性和可维护性
from typing import List, Tuple, Optional, Dict
# 导入日志记录模块，用于程序运行信息的记录
import logging
# 导入 OpenCV 库，用于图像处理
import cv2

# 配置日志记录的基本信息，设置日志级别为 INFO，指定日志格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# 导入 PyMuPDF 库，用于处理 PDF 文件
import fitz
# 从 shapely.geometry 模块导入几何对象相关功能，并重命名为 sg
import shapely.geometry as sg
# 从 shapely.geometry.base 模块导入基础几何对象类
from shapely.geometry.base import BaseGeometry
# 从 shapely.validation 模块导入几何对象有效性解释函数
from shapely.validation import explain_validity
# 导入并发处理模块，用于多线程任务执行
import concurrent.futures

# 导入 PyTorch 库，用于深度学习相关操作
import torch
# 从 transformers 库导入多模态条件生成模型类
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
# 从自定义的 qwen_vl_utils 模块导入视觉信息处理函数
from qwen_vl_utils import process_vision_info
# 导入 NumPy 库，用于科学计算
import numpy as np
# 再次导入 PyMuPDF 库，确保使用
import fitz
# 再次导入日志记录模块，确保使用
import logging
# 再次导入操作系统相关功能模块，确保使用
import os
# 定义输出目录名称
output_dir = 'output'
# 创建输出目录，如果目录已存在则不会报错
os.makedirs(output_dir, exist_ok=True)
# 从 PIL 库导入 Image 类，用于图像处理
from PIL import Image

# 从自定义的 rapid_layout 模块导入布局检测和可视化布局类
from rapid_layout import RapidLayout, VisLayout

# 初始化布局检测引擎，设置置信度阈值和模型类型
layout_engine = RapidLayout(conf_thres=0.5, model_type="pp_layout_cdla")

# 从预训练模型路径加载多模态条件生成模型，指定数据类型和设备映射
model = Qwen2VLForConditionalGeneration.from_pretrained("/home/<USER>/Downloads/Qwen2-VL-7B-Instruct/",
                                                        torch_dtype=torch.bfloat16,
                                                        device_map="auto")

# 定义图片最小像素数
min_pixels = 256*28*28
# 定义图片最大像素数
max_pixels = 1280*28*28
# 从预训练模型路径加载自动处理器，设置图片像素范围
processor = AutoProcessor.from_pretrained("/home/<USER>/Downloads/Qwen2-VL-7B-Instruct/", 
                                          min_pixels=min_pixels, 
                                          max_pixels=max_pixels)

# 定义默认提示信息，使用中文，可根据需求修改为其他语言
DEFAULT_PROMPT = """使用markdown语法，将图片中识别到的文字转换为markdown格式输出。你必须做到：
1. 输出和使用识别到的图片的相同的语言，例如，识别到英语的字段，输出的内容必须是英语。
2. 不要解释和输出无关的文字，直接输出图片中的内容。例如，严禁输出 “以下是我根据图片内容生成的markdown文本：”这样的例子，而是应该直接输出markdown。
3. 内容不要包含在```markdown ```中、段落公式使用 $$ $$ 的形式、行内公式使用 $ $ 的形式、忽略掉长直线、忽略掉页码。
再次强调，不要解释和输出无关的文字，直接输出图片中的内容。
"""
# 定义默认矩形区域提示信息
DEFAULT_RECT_PROMPT = """图片中用带颜色的矩形框和名称(%s)标注出了一些区域。如果区域是表格或者图片，使用 ![]() 的形式插入到输出内容中，否则直接输出文字内容。
"""
# 定义默认角色提示信息
DEFAULT_ROLE_PROMPT = """你是一个PDF文档解析器，使用markdown和latex语法输出图片的内容。
"""

def _parse_pdf_to_images(pdf_path: str, output_dir: str = './output') -> List[Tuple[str, List[str]]]:
    """
    将 PDF 文件解析为图片，并保存到指定目录。

    Args:
        pdf_path (str): PDF 文件的路径。
        output_dir (str, optional): 图片保存的目录，默认为 './output'。

    Returns:
        List[Tuple[str, List[str]]]: 包含页面图片路径和该页面矩形区域图片文件名列表的元组列表。
    """
    # 初始化图片信息列表
    image_infos = []
    # 打开 PDF 文件
    pdf_document = fitz.open(pdf_path)
    # 遍历 PDF 中的每一页
    for page_index, page in enumerate(pdf_document):
        # 初始化矩形区域图片文件名列表
        rect_images = []
        # 记录当前处理的页面信息
        logging.info(f'parse page: {page_index}')
        # 将页面转换为图片，设置缩放比例
        pix = page.get_pixmap(matrix=fitz.Matrix(4, 4))
        # 将图片数据转换为 PIL 图像对象
        pix = Image.frombytes('RGB', [pix.width, pix.height], pix.samples)
        # 使用布局检测引擎检测图片中的目标
        boxes, scores, class_names, elapse = layout_engine(pix)
        # 遍历检测到的目标
        for index, (class_name, box) in enumerate(zip(class_names, boxes)):
            # 如果目标类别是图片或表格
            if class_name == 'figure' or class_name == 'table':
                # 生成矩形区域图片文件名
                name = f'{page_index}_{index}.png'
                # 裁剪矩形区域图片
                sub_pix = pix.crop(box)
                # 保存矩形区域图片到指定目录
                sub_pix.save(os.path.join(output_dir, name))
                # 将矩形区域图片文件名添加到列表中
                rect_images.append(name)

        # 初始化过滤后的目标框、置信度和类别列表
        boxes_ = []
        scores_ = []
        class_names_ = []
        # 遍历检测到的目标，过滤出图片和表格目标
        for i, (class_name, box, score) in enumerate(zip(class_names, boxes, scores)):
            if class_name == 'figure' or class_name == 'table':
                boxes_.append(box)
                scores_.append(score)
                class_name = f'{page_index}_{i}.png'
                class_names_.append(class_name)
                
        # 生成页面图片路径
        page_image = os.path.join(output_dir, f'{page_index}.png')
        # 将 PIL 图像对象转换为 NumPy 数组
        pix = np.array(pix)
        # 将图像颜色空间从 RGB 转换为 BGR
        pix = cv2.cvtColor(pix, cv2.COLOR_RGB2BGR)
        # 打印过滤后的目标框、置信度和类别信息
        print(boxes_, scores_, class_names_)
        # 在图片上绘制检测框
        ploted_img = VisLayout.draw_detections(pix, boxes_, scores_, class_names_)
        # 如果绘制后的图片不为空
        if ploted_img is not None:
            # 保存绘制后的图片
            cv2.imwrite(page_image, ploted_img)
        # 将页面图片路径和矩形区域图片文件名列表添加到图片信息列表中
        image_infos.append((page_image, rect_images))
    # 关闭 PDF 文件
    pdf_document.close()
    return image_infos


def _gpt_parse_images(
        image_infos: List[Tuple[str, List[str]]],
        prompt_dict: Optional[Dict] = None,
        output_dir: str = './',
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        # model: str = 'gpt-4o',
        verbose: bool = False,
        gpt_worker: int = 1,
        **args
) -> str:
    """
    将图片解析为 Markdown 内容。

    Args:
        image_infos (List[Tuple[str, List[str]]]): 包含页面图片路径和该页面矩形区域图片文件名列表的元组列表。
        prompt_dict (Optional[Dict], optional): 提示信息字典，默认为 None。
        output_dir (str, optional): 输出 Markdown 文件的目录，默认为 './'。
        api_key (Optional[str], optional): API 密钥，默认为 None。
        base_url (Optional[str], optional): API 基础 URL，默认为 None。
        verbose (bool, optional): 是否显示详细信息，默认为 False。
        gpt_worker (int, optional): 线程池的最大工作线程数，默认为 1。

    Returns:
        str: 生成的 Markdown 内容。
    """

    # 如果提供了提示信息字典且包含 'prompt' 键
    if isinstance(prompt_dict, dict) and 'prompt' in prompt_dict:
        # 使用用户提供的提示信息
        prompt = prompt_dict['prompt']
        logging.info("prompt is provided, using user prompt.")
    else:
        # 使用默认提示信息
        prompt = DEFAULT_PROMPT
        logging.info("prompt is not provided, using default prompt.")
    # 如果提供了提示信息字典且包含 'rect_prompt' 键
    if isinstance(prompt_dict, dict) and 'rect_prompt' in prompt_dict:
        # 使用用户提供的矩形区域提示信息
        rect_prompt = prompt_dict['rect_prompt']
        logging.info("rect_prompt is provided, using user prompt.")
    else:
        # 使用默认矩形区域提示信息
        rect_prompt = DEFAULT_RECT_PROMPT
        logging.info("rect_prompt is not provided, using default prompt.")
    # 如果提供了提示信息字典且包含 'role_prompt' 键
    if isinstance(prompt_dict, dict) and 'role_prompt' in prompt_dict:
        # 使用用户提供的角色提示信息
        role_prompt = prompt_dict['role_prompt']
        logging.info("role_prompt is provided, using user prompt.")
    else:
        # 使用默认角色提示信息
        role_prompt = DEFAULT_ROLE_PROMPT
        logging.info("role_prompt is not provided, using default prompt.")

    def _process_page(index: int, image_info: Tuple[str, List[str]]) -> Tuple[int, str]:
        """
        处理单页图片，生成对应的 Markdown 内容。

        Args:
            index (int): 页面索引。
            image_info (Tuple[str, List[str]]): 包含页面图片路径和该页面矩形区域图片文件名列表的元组。

        Returns:
            Tuple[int, str]: 页面索引和生成的 Markdown 内容。
        """
        # 记录当前处理的页面信息
        logging.info(f'gpt parse page: {index}')

        # agent = Agent(role=role_prompt, api_key=api_key, base_url=base_url, disable_python_run=True, model=model, **args)
        # 解包页面图片路径和矩形区域图片文件名列表
        page_image, rect_images = image_info
        # 初始化本地提示信息
        local_prompt = prompt
        # 将角色提示信息添加到本地提示信息中
        local_prompt = role_prompt + local_prompt
        # 如果存在矩形区域图片
        if rect_images:
            # 将矩形区域提示信息添加到本地提示信息中
            local_prompt += rect_prompt % ', '.join(rect_images)
        # content = agent.run([local_prompt, {'image': page_image}], display=verbose)
        # 构建输入消息列表
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image",
                        "image": page_image,
                    },
                    {"type": "text", "text": local_prompt},
                ],
            }
        ]
        # 应用聊天模板生成输入文本
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        # 打印生成的输入文本
        print(text)
        # 处理视觉信息
        image_inputs, video_inputs = process_vision_info(messages)
        # 使用处理器处理输入文本和视觉信息
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        # 将输入数据移动到 GPU 上
        inputs = inputs.to("cuda")

        # 推理：生成输出
        generated_ids = model.generate(**inputs, max_new_tokens=2000, num_beams=1)
        # 裁剪生成的 ID 序列
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        # 解码生成的 ID 序列为文本
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        return index, output_text

    # 初始化 Markdown 内容列表
    contents = [None] * len(image_infos)
    # 创建线程池，设置最大工作线程数
    with concurrent.futures.ThreadPoolExecutor(max_workers=gpt_worker) as executor:
        # 提交任务到线程池
        futures = [executor.submit(_process_page, index, image_info) for index, image_info in enumerate(image_infos)]
        # 遍历完成的任务
        for future in concurrent.futures.as_completed(futures):
            # 获取任务结果
            index, content = future.result()
            # 提取生成的 Markdown 内容
            content = content[0]
            # 打印生成的 Markdown 内容
            print(content)

            # 在某些情况下大模型还是会输出 ```markdown ```字符串
            if '```markdown' in content:
                # 移除 ```markdown\n 字符串
                content = content.replace('```markdown\n', '')
                # 查找最后一个 ``` 的位置
                last_backticks_pos = content.rfind('```')
                if last_backticks_pos != -1:
                    # 移除最后一个 ``` 及其后面的字符串
                    content = content[:last_backticks_pos] + content[last_backticks_pos + 3:]

            # 将处理后的 Markdown 内容添加到列表中
            contents[index] = content

    # 生成输出 Markdown 文件路径
    output_path = os.path.join(output_dir, 'output.md')
    # 打开输出 Markdown 文件并写入内容
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n\n'.join(contents))

    return '\n\n'.join(contents)


def parse_pdf(
        pdf_path: str,
        output_dir: str = './',
        prompt: Optional[Dict] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model: str = 'gpt-4o',
        verbose: bool = False,
        gpt_worker: int = 1,
        **args
) -> Tuple[str, List[str]]:
    """
    将 PDF 文件解析为 Markdown 文件。

    Args:
        pdf_path (str): PDF 文件的路径。
        output_dir (str, optional): 输出目录，默认为 './'。
        prompt (Optional[Dict], optional): 提示信息字典，默认为 None。
        api_key (Optional[str], optional): API 密钥，默认为 None。
        base_url (Optional[str], optional): API 基础 URL，默认为 None。
        model (str, optional): 模型名称，默认为 'gpt-4o'。
        verbose (bool, optional): 是否显示详细信息，默认为 False。
        gpt_worker (int, optional): 线程池的最大工作线程数，默认为 1。

    Returns:
        Tuple[str, List[str]]: 生成的 Markdown 内容和所有矩形区域图片文件名列表。
    """
    # 如果输出目录不存在，则创建该目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 将 PDF 文件解析为图片
    image_infos = _parse_pdf_to_images(pdf_path, output_dir=output_dir)
    # 打印图片信息
    print(image_infos)
    # 将图片解析为 Markdown 内容
    content = _gpt_parse_images(
        image_infos=image_infos,
        output_dir=output_dir,
        prompt_dict=prompt,
        api_key=api_key,
        base_url=base_url,
        model=model,
        verbose=verbose,
        gpt_worker=gpt_worker,
        **args
    )

    # 初始化所有矩形区域图片文件名列表
    all_rect_images = []
    # 如果不显示详细信息，则删除所有页面图片
    if not verbose:
        for page_image, rect_images in image_infos:
            if os.path.exists(page_image):
                os.remove(page_image)
            all_rect_images.extend(rect_images)
    return content, all_rect_images


# 调用 parse_pdf 函数处理 PDF 文件
result = parse_pdf(
    pdf_path='/home/<USER>/wyf/test.pdf',
    output_dir="./output",
    verbose=True,
    gpt_worker=1
)
