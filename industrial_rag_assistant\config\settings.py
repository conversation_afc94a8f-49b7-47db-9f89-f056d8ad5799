"""
配置文件 - 工业智维助手系统配置
"""
import os
from pathlib import Path
from typing import Dict, Any
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    from pydantic import BaseSettings, Field

class Settings(BaseSettings):
    """系统配置类"""
    
    # 项目基础路径
    PROJECT_ROOT: Path = Path(__file__).parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    INPUT_DIR: Path = DATA_DIR / "input"
    OUTPUT_DIR: Path = DATA_DIR / "output"
    TEMP_DIR: Path = DATA_DIR / "temp"
    MODELS_DIR: Path = PROJECT_ROOT / "models"
    
    # 模型配置
    MULTIMODAL_MODEL_PATH: str = Field(
        default="/path/to/Qwen2.5-VL-7B-Instruct",
        description="多模态模型路径"
    )
    
    LLM_MODEL_PATH: str = Field(
        default="/path/to/Qwen2.5-7B-Instruct", 
        description="语言模型路径"
    )
    
    EMBEDDING_MODEL_NAME: str = Field(
        default="BAAI/bge-large-zh-v1.5",
        description="嵌入模型名称"
    )
    
    # 设备配置
    DEVICE: str = Field(default="auto", description="计算设备")
    USE_GPU: bool = Field(default=True, description="是否使用GPU")
    MAX_GPU_MEMORY: str = Field(default="20GB", description="最大GPU内存")
    
    # PDF处理配置
    PDF_DPI: int = Field(default=200, description="PDF转图像DPI")
    PDF_FORMAT: str = Field(default="PNG", description="PDF转图像格式")
    
    # 布局分析配置
    LAYOUT_CONF_THRESHOLD: float = Field(default=0.5, description="布局检测置信度阈值")
    LAYOUT_MODEL_TYPE: str = Field(default="pp_layout_cdla", description="布局检测模型类型")
    
    # 表格识别配置
    TABLE_MODEL_NAME: str = Field(
        default="iic/cv_dla34_table-structure-recognition_cycle-centernet",
        description="表格结构识别模型"
    )
    
    # OCR配置
    OCR_USE_GPU: bool = Field(default=True, description="OCR是否使用GPU")
    OCR_LANG: str = Field(default="ch", description="OCR识别语言")
    
    # IoT算法配置
    IOT_THRESHOLD: float = Field(default=0.5, description="IoT算法阈值")
    
    # 文本分块配置
    CHUNK_SIZE: int = Field(default=500, description="文本块大小")
    CHUNK_OVERLAP: int = Field(default=50, description="文本块重叠大小")
    
    # 向量数据库配置
    VECTOR_DB_TYPE: str = Field(default="faiss", description="向量数据库类型")
    VECTOR_DIMENSION: int = Field(default=1024, description="向量维度")
    
    # 检索配置
    TOP_K: int = Field(default=5, description="检索返回数量")
    BM25_K1: float = Field(default=1.2, description="BM25参数k1")
    BM25_B: float = Field(default=0.75, description="BM25参数b")
    
    # RRF融合配置
    RRF_K: int = Field(default=60, description="RRF算法参数k")
    VECTOR_WEIGHT: float = Field(default=0.7, description="向量检索权重")
    BM25_WEIGHT: float = Field(default=0.3, description="BM25检索权重")
    
    # 生成配置
    MAX_NEW_TOKENS: int = Field(default=2000, description="最大生成token数")
    TEMPERATURE: float = Field(default=0.1, description="生成温度")
    TOP_P: float = Field(default=0.9, description="Top-p采样参数")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: str = Field(default="industrial_rag.log", description="日志文件")
    
    # Prompt模板
    SYSTEM_PROMPT: str = """你是一个专业的工业设备技术专家，擅长设备故障诊断和技术问答。
请基于提供的技术手册内容，准确回答用户的问题。
如果问题涉及故障诊断，请提供详细的诊断步骤和解决方案。
如果信息不足，请明确说明需要更多信息。"""
    
    USER_PROMPT_TEMPLATE: str = """基于以下技术手册内容回答问题：

相关内容：
{context}

问题：{question}

请提供详细、准确的回答："""
    
    TABLE_EXTRACTION_PROMPT: str = """请将图片中的表格内容转换为Markdown格式。
要求：
1. 保持表格结构完整
2. 准确识别表头和数据行
3. 使用标准Markdown表格语法
4. 如果有合并单元格，请合理处理"""
    
    IMAGE_DESCRIPTION_PROMPT: str = """请详细描述这张图片的内容，将其转换为Markdown格式的文本。
要求：
1. 如果图片中有文字，请准确识别
2. 描述图片中的技术信息、图表、示意图等
3. 保持技术术语的准确性
4. 使用Markdown格式组织内容"""
    
    TEXT_EXTRACTION_PROMPT: str = """请将图片中的文字内容转换为Markdown格式。
要求：
1. 识别标题、段落、列表等结构
2. 保持原有的层次结构
3. 使用Markdown语法格式化
4. 准确识别技术术语和数值"""
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 创建必要的目录
        self.create_directories()
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.DATA_DIR,
            self.INPUT_DIR, 
            self.OUTPUT_DIR,
            self.TEMP_DIR,
            self.MODELS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return {
            "multimodal_model_path": self.MULTIMODAL_MODEL_PATH,
            "llm_model_path": self.LLM_MODEL_PATH,
            "embedding_model_name": self.EMBEDDING_MODEL_NAME,
            "device": self.DEVICE,
            "use_gpu": self.USE_GPU,
            "max_gpu_memory": self.MAX_GPU_MEMORY
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """获取处理配置"""
        return {
            "pdf_dpi": self.PDF_DPI,
            "pdf_format": self.PDF_FORMAT,
            "layout_conf_threshold": self.LAYOUT_CONF_THRESHOLD,
            "layout_model_type": self.LAYOUT_MODEL_TYPE,
            "table_model_name": self.TABLE_MODEL_NAME,
            "ocr_use_gpu": self.OCR_USE_GPU,
            "ocr_lang": self.OCR_LANG,
            "iot_threshold": self.IOT_THRESHOLD
        }
    
    def get_rag_config(self) -> Dict[str, Any]:
        """获取RAG配置"""
        return {
            "chunk_size": self.CHUNK_SIZE,
            "chunk_overlap": self.CHUNK_OVERLAP,
            "vector_db_type": self.VECTOR_DB_TYPE,
            "vector_dimension": self.VECTOR_DIMENSION,
            "top_k": self.TOP_K,
            "bm25_k1": self.BM25_K1,
            "bm25_b": self.BM25_B,
            "rrf_k": self.RRF_K,
            "vector_weight": self.VECTOR_WEIGHT,
            "bm25_weight": self.BM25_WEIGHT
        }

# 全局配置实例
settings = Settings()
