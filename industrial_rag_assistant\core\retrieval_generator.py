"""
智能检索与生成器 - 融合BM25和向量检索的RAG问答系统
"""
import torch
from typing import List, Dict, Any, Optional, Tuple
import logging
from pathlib import Path

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from vllm import LLM, SamplingParams
    from langchain.schema import Document
except ImportError:
    logging.warning("Required packages not installed. Please install transformers, vllm, langchain")
    AutoTokenizer = None
    AutoModelForCausalLM = None
    LLM = None
    SamplingParams = None
    Document = None

try:
    from ..config.settings import settings
    from ..utils.text_processor import TextProcessor
except ImportError:
    from config.settings import settings
    from utils.text_processor import TextProcessor

logger = logging.getLogger(__name__)

class RetrievalGenerator:
    """智能检索与生成器"""
    
    def __init__(self, llm_model_path: str = None, use_vllm: bool = True):
        """
        初始化检索生成器
        
        Args:
            llm_model_path: LLM模型路径
            use_vllm: 是否使用vLLM加速
        """
        self.llm_model_path = llm_model_path or settings.LLM_MODEL_PATH
        self.use_vllm = use_vllm
        self.device = settings.DEVICE
        
        # 初始化组件
        self.text_processor = TextProcessor()
        self.tokenizer = None
        self.model = None
        self.llm = None
        self.sampling_params = None
        
        # 初始化模型
        self._initialize_model()
        
        logger.info(f"Retrieval generator initialized with model: {self.llm_model_path}")
    
    def _initialize_model(self):
        """初始化语言模型"""
        try:
            if not Path(self.llm_model_path).exists():
                raise FileNotFoundError(f"Model not found: {self.llm_model_path}")
            
            if self.use_vllm and LLM is not None:
                # 使用vLLM加速推理
                self.llm = LLM(
                    model=self.llm_model_path,
                    trust_remote_code=True,
                    max_model_len=8192,
                    gpu_memory_utilization=0.8
                )
                self.sampling_params = SamplingParams(
                    temperature=settings.TEMPERATURE,
                    top_p=settings.TOP_P,
                    max_tokens=settings.MAX_NEW_TOKENS
                )
                logger.info("vLLM model loaded successfully")
            else:
                # 使用transformers
                if AutoTokenizer is None or AutoModelForCausalLM is None:
                    raise ImportError("Transformers not available")
                
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.llm_model_path,
                    trust_remote_code=True
                )
                
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.llm_model_path,
                    torch_dtype=torch.bfloat16,
                    device_map="auto",
                    trust_remote_code=True
                )
                logger.info("Transformers model loaded successfully")
                
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    def hybrid_retrieve(self, query: str, vector_store, bm25_retriever, 
                       top_k: int = None) -> List[Document]:
        """
        混合检索：融合BM25和向量检索
        
        Args:
            query: 查询文本
            vector_store: 向量存储
            bm25_retriever: BM25检索器
            top_k: 返回文档数量
            
        Returns:
            检索到的文档列表
        """
        try:
            top_k = top_k or settings.TOP_K
            
            # 向量检索
            vector_docs = []
            if vector_store is not None:
                vector_docs = vector_store.similarity_search(query, k=top_k)
                logger.info(f"Vector search returned {len(vector_docs)} documents")
            
            # BM25检索
            bm25_docs = []
            if bm25_retriever is not None:
                # 预处理查询
                query_tokens = self.text_processor.preprocess_for_bm25(query)
                processed_query = ' '.join(query_tokens)
                
                bm25_docs = bm25_retriever.get_relevant_documents(processed_query)
                # 限制返回数量
                bm25_docs = bm25_docs[:top_k]
                logger.info(f"BM25 search returned {len(bm25_docs)} documents")
            
            # 融合结果使用RRF算法
            fused_docs = self._reciprocal_rank_fusion(
                vector_docs, bm25_docs, top_k
            )
            
            logger.info(f"Hybrid retrieval returned {len(fused_docs)} documents")
            return fused_docs
            
        except Exception as e:
            logger.error(f"Hybrid retrieval failed: {e}")
            return []
    
    def _reciprocal_rank_fusion(self, vector_docs: List[Document], 
                              bm25_docs: List[Document], 
                              top_k: int) -> List[Document]:
        """
        倒数排名融合算法 (Reciprocal Rank Fusion)
        
        Args:
            vector_docs: 向量检索结果
            bm25_docs: BM25检索结果
            top_k: 返回文档数量
            
        Returns:
            融合后的文档列表
        """
        try:
            # RRF参数
            k = settings.RRF_K
            
            # 收集所有文档并计算RRF分数
            doc_scores = {}
            
            # 处理向量检索结果
            for rank, doc in enumerate(vector_docs):
                doc_id = self._get_doc_id(doc)
                if doc_id not in doc_scores:
                    doc_scores[doc_id] = {'doc': doc, 'score': 0.0}
                doc_scores[doc_id]['score'] += 1.0 / (k + rank + 1)
            
            # 处理BM25检索结果
            for rank, doc in enumerate(bm25_docs):
                doc_id = self._get_doc_id(doc)
                if doc_id not in doc_scores:
                    doc_scores[doc_id] = {'doc': doc, 'score': 0.0}
                doc_scores[doc_id]['score'] += 1.0 / (k + rank + 1)
            
            # 按分数排序
            sorted_docs = sorted(
                doc_scores.values(), 
                key=lambda x: x['score'], 
                reverse=True
            )
            
            # 返回top_k文档
            fused_docs = [item['doc'] for item in sorted_docs[:top_k]]
            
            return fused_docs
            
        except Exception as e:
            logger.error(f"RRF fusion failed: {e}")
            # 备用方案：简单合并
            all_docs = vector_docs + bm25_docs
            return all_docs[:top_k]
    
    def _get_doc_id(self, doc: Document) -> str:
        """
        获取文档ID
        
        Args:
            doc: 文档对象
            
        Returns:
            文档ID
        """
        if hasattr(doc, 'metadata'):
            metadata = doc.metadata
        else:
            metadata = doc.get('metadata', {})
        
        # 尝试多种ID字段
        for id_field in ['id', 'doc_id', 'chunk_id', 'source']:
            if id_field in metadata:
                return str(metadata[id_field])
        
        # 使用内容哈希作为ID
        content = doc.page_content if hasattr(doc, 'page_content') else doc.get('page_content', '')
        return str(hash(content))
    
    def generate_answer(self, query: str, retrieved_docs: List[Document], 
                       context_type: str = "general") -> str:
        """
        基于检索文档生成答案
        
        Args:
            query: 用户查询
            retrieved_docs: 检索到的文档
            context_type: 上下文类型
            
        Returns:
            生成的答案
        """
        try:
            # 构建上下文
            context = self._build_context(retrieved_docs)
            
            # 选择提示词模板
            prompt_template = self._get_prompt_template(context_type)
            
            # 构建完整提示词
            prompt = prompt_template.format(
                context=context,
                question=query
            )
            
            # 生成答案
            if self.use_vllm and self.llm is not None:
                answer = self._generate_with_vllm(prompt)
            else:
                answer = self._generate_with_transformers(prompt)
            
            # 后处理答案
            answer = self._post_process_answer(answer)
            
            logger.info("Answer generated successfully")
            return answer
            
        except Exception as e:
            logger.error(f"Answer generation failed: {e}")
            return "抱歉，我无法生成答案。请重新提问。"
    
    def _build_context(self, docs: List[Document]) -> str:
        """
        构建上下文
        
        Args:
            docs: 文档列表
            
        Returns:
            上下文文本
        """
        if not docs:
            return ""
        
        context_parts = []
        for i, doc in enumerate(docs):
            content = doc.page_content if hasattr(doc, 'page_content') else doc.get('page_content', '')
            if content.strip():
                context_parts.append(f"文档{i+1}：{content}")
        
        return '\n\n'.join(context_parts)
    
    def _get_prompt_template(self, context_type: str) -> str:
        """
        获取提示词模板
        
        Args:
            context_type: 上下文类型
            
        Returns:
            提示词模板
        """
        templates = {
            "general": settings.QA_PROMPT_TEMPLATE,
            "technical": settings.QA_PROMPT_TEMPLATE,
            "maintenance": settings.QA_PROMPT_TEMPLATE
        }
        
        return templates.get(context_type, templates["general"])
    
    def _generate_with_vllm(self, prompt: str) -> str:
        """
        使用vLLM生成答案
        
        Args:
            prompt: 提示词
            
        Returns:
            生成的答案
        """
        try:
            outputs = self.llm.generate([prompt], self.sampling_params)
            return outputs[0].outputs[0].text
            
        except Exception as e:
            logger.error(f"vLLM generation failed: {e}")
            raise
    
    def _generate_with_transformers(self, prompt: str) -> str:
        """
        使用transformers生成答案
        
        Args:
            prompt: 提示词
            
        Returns:
            生成的答案
        """
        try:
            # 编码输入
            inputs = self.tokenizer(
                prompt, 
                return_tensors="pt", 
                truncation=True, 
                max_length=4096
            )
            inputs = inputs.to(self.model.device)
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=settings.MAX_NEW_TOKENS,
                    temperature=settings.TEMPERATURE,
                    top_p=settings.TOP_P,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码
            generated_text = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            return generated_text
            
        except Exception as e:
            logger.error(f"Transformers generation failed: {e}")
            raise
    
    def _post_process_answer(self, answer: str) -> str:
        """
        后处理答案
        
        Args:
            answer: 原始答案
            
        Returns:
            处理后的答案
        """
        if not answer:
            return "抱歉，我无法生成答案。"
        
        # 移除多余的空白
        answer = answer.strip()
        
        # 移除可能的重复内容
        lines = answer.split('\n')
        unique_lines = []
        for line in lines:
            if line.strip() and line not in unique_lines:
                unique_lines.append(line)
        
        return '\n'.join(unique_lines)
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            if hasattr(self, 'model'):
                del self.model
            if hasattr(self, 'llm'):
                del self.llm
            if hasattr(self, 'tokenizer'):
                del self.tokenizer
            
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info("Resources cleaned up successfully")
            
        except Exception as e:
            logger.warning(f"Resource cleanup failed: {e}")
