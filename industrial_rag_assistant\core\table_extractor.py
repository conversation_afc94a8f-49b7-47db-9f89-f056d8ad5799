"""
高精度表格提取器 - 融合ModelScope TSR和PaddleOCR的表格提取
"""
import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from PIL import Image
import logging

try:
    from modelscope.pipelines import pipeline
    from modelscope.utils.constant import Tasks
except ImportError:
    logging.warning("ModelScope not installed. Please install it for table recognition.")
    pipeline = None
    Tasks = None

try:
    from ..config.settings import settings
except ImportError:
    from config.settings import settings
try:
    from ..utils.ocr_processor import OCRProcessor
except ImportError:
    from utils.ocr_processor import OCRProcessor

logger = logging.getLogger(__name__)

class TableExtractor:
    """高精度表格提取器"""
    
    def __init__(self, table_model_name: str = None):
        """
        初始化表格提取器
        
        Args:
            table_model_name: 表格识别模型名称
        """
        self.table_model_name = table_model_name or settings.TABLE_MODEL_NAME
        self.iot_threshold = settings.IOT_THRESHOLD
        
        # 初始化表格识别模型
        if pipeline is None or Tasks is None:
            raise ImportError("ModelScope is required for table recognition")
        
        try:
            self.table_recognition = pipeline(
                Tasks.table_recognition, 
                model=self.table_model_name
            )
            logger.info(f"Table recognition model loaded: {self.table_model_name}")
        except Exception as e:
            logger.error(f"Failed to load table recognition model: {e}")
            raise
        
        # 初始化OCR处理器
        self.ocr_processor = OCRProcessor()
    
    def extract_table(self, table_image: Image.Image) -> Dict[str, Any]:
        """
        提取表格内容
        
        Args:
            table_image: 表格图像
            
        Returns:
            表格提取结果
        """
        try:
            logger.info("Starting table extraction...")
            
            # 1. 表格结构识别
            structure_result = self._recognize_table_structure(table_image)
            
            # 2. OCR文字识别
            ocr_result = self._recognize_table_text(table_image)
            
            # 3. 文字与单元格匹配
            cell_text_mapping = self._match_text_to_cells(
                structure_result, ocr_result
            )
            
            # 4. 生成结构化表格数据
            structured_table = self._build_structured_table(
                structure_result, cell_text_mapping
            )
            
            # 5. 转换为Markdown
            markdown_table = self._convert_to_markdown(structured_table)
            
            result = {
                'structure': structure_result,
                'ocr_results': ocr_result,
                'cell_mapping': cell_text_mapping,
                'structured_table': structured_table,
                'markdown': markdown_table,
                'extraction_success': True
            }
            
            logger.info("Table extraction completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Table extraction failed: {e}")
            return {
                'extraction_success': False,
                'error': str(e),
                'markdown': ""
            }
    
    def _recognize_table_structure(self, table_image: Image.Image) -> Dict[str, Any]:
        """
        识别表格结构
        
        Args:
            table_image: 表格图像
            
        Returns:
            表格结构识别结果
        """
        try:
            # 转换为numpy数组
            if isinstance(table_image, Image.Image):
                image_array = np.array(table_image)
            else:
                image_array = table_image
            
            # 执行表格结构识别
            structure_result = self.table_recognition(image_array)
            
            logger.info("Table structure recognition completed")
            return structure_result
            
        except Exception as e:
            logger.error(f"Table structure recognition failed: {e}")
            raise
    
    def _recognize_table_text(self, table_image: Image.Image) -> List[Dict[str, Any]]:
        """
        识别表格中的文字
        
        Args:
            table_image: 表格图像
            
        Returns:
            OCR识别结果
        """
        try:
            # 执行OCR识别
            ocr_results = self.ocr_processor.recognize_text(table_image)
            
            # 过滤低质量结果
            filtered_results = self.ocr_processor.filter_text_results(
                ocr_results, min_confidence=0.6
            )
            
            logger.info(f"Table text recognition completed: {len(filtered_results)} text regions")
            return filtered_results

        except Exception as e:
            logger.error(f"Table text recognition failed: {e}")
            raise

    def _match_text_to_cells(self, structure_result: Dict[str, Any],
                           ocr_results: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        使用IoT算法将OCR文字匹配到表格单元格

        Args:
            structure_result: 表格结构识别结果
            ocr_results: OCR识别结果

        Returns:
            单元格文字映射
        """
        try:
            # 提取单元格坐标
            cells = self._extract_cell_coordinates(structure_result)

            # 初始化单元格文字映射
            cell_text_mapping = {}
            for i, cell in enumerate(cells):
                cell_text_mapping[f"cell_{i}"] = []

            # 为每个OCR文字找到最佳匹配的单元格
            for ocr_result in ocr_results:
                best_cell_id = self._find_best_matching_cell(ocr_result, cells)
                if best_cell_id is not None:
                    cell_text_mapping[best_cell_id].append(ocr_result['text'])

            # 合并每个单元格的文字
            for cell_id in cell_text_mapping:
                if cell_text_mapping[cell_id]:
                    cell_text_mapping[cell_id] = ' '.join(cell_text_mapping[cell_id])
                else:
                    cell_text_mapping[cell_id] = ""

            logger.info("Text to cell matching completed")
            return cell_text_mapping

        except Exception as e:
            logger.error(f"Text to cell matching failed: {e}")
            raise

    def _extract_cell_coordinates(self, structure_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从表格结构识别结果中提取单元格坐标

        Args:
            structure_result: 表格结构识别结果

        Returns:
            单元格坐标列表
        """
        cells = []

        # 根据ModelScope表格识别结果的格式解析单元格
        # 这里需要根据实际的ModelScope输出格式进行调整
        if 'polygons' in structure_result:
            for i, polygon in enumerate(structure_result['polygons']):
                # 计算边界框
                x_coords = [point[0] for point in polygon]
                y_coords = [point[1] for point in polygon]

                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)

                cell = {
                    'id': f"cell_{i}",
                    'bbox': [x1, y1, x2, y2],
                    'polygon': polygon,
                    'center': [(x1 + x2) / 2, (y1 + y2) / 2],
                    'area': (x2 - x1) * (y2 - y1)
                }
                cells.append(cell)

        return cells

    def _find_best_matching_cell(self, ocr_result: Dict[str, Any],
                               cells: List[Dict[str, Any]]) -> Optional[str]:
        """
        为OCR文字找到最佳匹配的单元格

        Args:
            ocr_result: OCR识别结果
            cells: 单元格列表

        Returns:
            最佳匹配单元格ID
        """
        best_cell_id = None
        best_iot_score = 0.0

        ocr_bbox = ocr_result['bbox']

        for cell in cells:
            cell_bbox = cell['bbox']

            # 计算IoT (Intersection over Text area)
            iot_score = self._calculate_iot(ocr_bbox, cell_bbox)

            if iot_score > best_iot_score and iot_score > self.iot_threshold:
                best_iot_score = iot_score
                best_cell_id = cell['id']

        return best_cell_id

    def _calculate_iot(self, text_bbox: List[float], cell_bbox: List[float]) -> float:
        """
        计算IoT (Intersection over Text area)

        Args:
            text_bbox: 文字边界框 [x1, y1, x2, y2]
            cell_bbox: 单元格边界框 [x1, y1, x2, y2]

        Returns:
            IoT分数
        """
        # 计算交集
        x1 = max(text_bbox[0], cell_bbox[0])
        y1 = max(text_bbox[1], cell_bbox[1])
        x2 = min(text_bbox[2], cell_bbox[2])
        y2 = min(text_bbox[3], cell_bbox[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection_area = (x2 - x1) * (y2 - y1)
        text_area = (text_bbox[2] - text_bbox[0]) * (text_bbox[3] - text_bbox[1])

        if text_area == 0:
            return 0.0

        return intersection_area / text_area

    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """
        计算IoU (Intersection over Union)

        Args:
            bbox1: 边界框1 [x1, y1, x2, y2]
            bbox2: 边界框2 [x1, y1, x2, y2]

        Returns:
            IoU分数
        """
        # 计算交集
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection_area = (x2 - x1) * (y2 - y1)

        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union_area = area1 + area2 - intersection_area

        if union_area == 0:
            return 0.0

        return intersection_area / union_area

    def _build_structured_table(self, structure_result: Dict[str, Any],
                              cell_text_mapping: Dict[str, str]) -> List[List[str]]:
        """
        构建结构化表格数据

        Args:
            structure_result: 表格结构识别结果
            cell_text_mapping: 单元格文字映射

        Returns:
            结构化表格数据
        """
        try:
            # 这里需要根据实际的表格结构识别结果来构建表格
            # 简化实现：假设表格是规则的矩形网格

            # 提取所有单元格并按位置排序
            cells_with_text = []
            for cell_id, text in cell_text_mapping.items():
                if cell_id in structure_result.get('cell_positions', {}):
                    position = structure_result['cell_positions'][cell_id]
                    cells_with_text.append({
                        'row': position.get('row', 0),
                        'col': position.get('col', 0),
                        'text': text
                    })

            # 如果没有位置信息，尝试根据坐标推断
            if not cells_with_text:
                cells_with_text = self._infer_table_structure(cell_text_mapping, structure_result)

            # 构建表格矩阵
            if cells_with_text:
                max_row = max(cell['row'] for cell in cells_with_text)
                max_col = max(cell['col'] for cell in cells_with_text)

                table_matrix = [["" for _ in range(max_col + 1)] for _ in range(max_row + 1)]

                for cell in cells_with_text:
                    table_matrix[cell['row']][cell['col']] = cell['text']

                return table_matrix
            else:
                # 备用方案：简单的单行表格
                return [list(cell_text_mapping.values())]

        except Exception as e:
            logger.error(f"Building structured table failed: {e}")
            # 返回简单的单行表格作为备用
            return [list(cell_text_mapping.values())]

    def _infer_table_structure(self, cell_text_mapping: Dict[str, str],
                             structure_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        根据单元格坐标推断表格结构

        Args:
            cell_text_mapping: 单元格文字映射
            structure_result: 表格结构识别结果

        Returns:
            带位置信息的单元格列表
        """
        cells_with_position = []

        # 提取单元格坐标
        cells = self._extract_cell_coordinates(structure_result)

        # 按Y坐标分组（行）
        y_groups = {}
        for cell in cells:
            y_center = cell['center'][1]
            # 找到相近的Y坐标组
            found_group = False
            for group_y in y_groups:
                if abs(y_center - group_y) < 20:  # 阈值可调整
                    y_groups[group_y].append(cell)
                    found_group = True
                    break
            if not found_group:
                y_groups[y_center] = [cell]

        # 为每行按X坐标排序
        sorted_rows = []
        for y_coord in sorted(y_groups.keys()):
            row_cells = sorted(y_groups[y_coord], key=lambda x: x['center'][0])
            sorted_rows.append(row_cells)

        # 分配行列索引
        for row_idx, row_cells in enumerate(sorted_rows):
            for col_idx, cell in enumerate(row_cells):
                cell_id = cell['id']
                if cell_id in cell_text_mapping:
                    cells_with_position.append({
                        'row': row_idx,
                        'col': col_idx,
                        'text': cell_text_mapping[cell_id]
                    })

        return cells_with_position

    def _convert_to_markdown(self, structured_table: List[List[str]]) -> str:
        """
        将结构化表格转换为Markdown格式

        Args:
            structured_table: 结构化表格数据

        Returns:
            Markdown格式的表格
        """
        if not structured_table:
            return ""

        try:
            markdown_lines = []

            # 处理表头
            if structured_table:
                header_row = structured_table[0]
                # 清理和格式化表头
                cleaned_header = [cell.strip().replace('|', '\\|') for cell in header_row]
                markdown_lines.append('| ' + ' | '.join(cleaned_header) + ' |')

                # 添加分隔行
                separator = '| ' + ' | '.join(['---'] * len(header_row)) + ' |'
                markdown_lines.append(separator)

                # 处理数据行
                for row in structured_table[1:]:
                    # 确保行长度与表头一致
                    while len(row) < len(header_row):
                        row.append("")

                    # 清理和格式化数据
                    cleaned_row = [cell.strip().replace('|', '\\|') for cell in row[:len(header_row)]]
                    markdown_lines.append('| ' + ' | '.join(cleaned_row) + ' |')

            return '\n'.join(markdown_lines)

        except Exception as e:
            logger.error(f"Markdown conversion failed: {e}")
            # 备用方案：简单的文本格式
            return '\n'.join(['\t'.join(row) for row in structured_table])

    def adjust_coordinates(self, ocr_results: List[Dict[str, Any]],
                          adjustment_threshold: float = 10.0) -> List[Dict[str, Any]]:
        """
        调整OCR结果的Y坐标，使同一行的文本对齐

        Args:
            ocr_results: OCR识别结果
            adjustment_threshold: 调整阈值

        Returns:
            调整后的OCR结果
        """
        if not ocr_results:
            return ocr_results

        # 按Y坐标分组
        y_groups = {}
        for result in ocr_results:
            y_center = result['center'][1]

            # 找到相近的Y坐标组
            found_group = False
            for group_y in y_groups:
                if abs(y_center - group_y) < adjustment_threshold:
                    y_groups[group_y].append(result)
                    found_group = True
                    break

            if not found_group:
                y_groups[y_center] = [result]

        # 调整每组的Y坐标为平均值
        adjusted_results = []
        for group_y, group_results in y_groups.items():
            if len(group_results) > 1:
                # 计算平均Y坐标
                avg_y = sum(r['center'][1] for r in group_results) / len(group_results)

                # 调整每个结果的Y坐标
                for result in group_results:
                    adjusted_result = result.copy()
                    y_offset = avg_y - result['center'][1]

                    # 更新边界框
                    adjusted_result['bbox'][1] += y_offset
                    adjusted_result['bbox'][3] += y_offset
                    adjusted_result['center'][1] = avg_y

                    adjusted_results.append(adjusted_result)
            else:
                adjusted_results.extend(group_results)

        return adjusted_results
